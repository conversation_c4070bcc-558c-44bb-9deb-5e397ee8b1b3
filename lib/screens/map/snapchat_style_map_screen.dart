import 'package:bop_maps/screens/map/constants/map_style.dart';
import 'package:bop_maps/screens/map/models/map_style_config.dart';
import 'package:bop_maps/screens/map/widgets/map_style_toggle_widget.dart';
import 'package:bop_maps/screens/map/widgets/pins_in_aura_indicator_widget.dart';
import 'package:bop_maps/screens/map/widgets/pins_in_aura_bottomsheet_widget.dart';
import 'package:bop_maps/screens/map/widgets/pin_selection_bottomsheet_widget.dart';
import 'package:flutter/material.dart';
import 'package:maplibre_gl/maplibre_gl.dart';
import 'package:provider/provider.dart';
import 'package:geolocator/geolocator.dart';
import 'package:flutter_compass/flutter_compass.dart';
import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/services.dart';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

import '../../providers/map_provider.dart';
import 'services/cluster_service.dart';
import 'services/pin_feature_service.dart';
import '../../providers/theme_provider.dart';
import '../../providers/skin_provider.dart';
import '../../config/constants.dart';
import '../../config/route_constants.dart';
import '../../widgets/navigation/bottom_navigation_bar.dart';
import '../../utils/navigation_helper.dart';

import '../../models/pin_skin.dart';
import '../../models/pin.dart';
import '../../widgets/bottomsheets/nearby_pin_bottomsheet.dart';
import '../../widgets/bottomsheets/distant_pin_bottomsheet.dart';
import 'package:bop_maps/screens/map/painters/weather_effects_painter.dart';
import 'managers/map_style_manager.dart';
import 'managers/visual_effects_manager.dart';
import 'managers/weather_visual_effects_manager.dart'; // Add this import
import 'managers/enhanced_gesture_manager.dart';
import 'managers/layer_state_machine.dart';
import 'managers/smart_location_fetch_manager.dart';
import '../../services/events/pin_event_bus.dart';

/*
 * PIN FETCHING & RENDERING OPTIMIZATIONS
 * ======================================
 * 
 * This file has been optimized to prevent pins from disappearing/reappearing and
 * make clustering instantaneous. Key optimizations include:
 * 
 * PROGRESSIVE PIN RENDERING (NEW):
 * - Pins are rendered progressively as they become available instead of waiting for all pins to load
 * - Automatically enabled for 5+ pins to improve perceived performance
 * - Renders 3 pins every 200ms for smooth visual feedback
 * - Shows empty layers immediately, then populates them incrementally
 * - Supports both individual pins and clustering during progressive rendering
 * - Includes debug info overlay in debug mode to monitor rendering progress
 * - Can be toggled on/off via toggleProgressiveRendering() method
 * - Extensive debug logging shows pin count checks and rendering progress
 * 
 * 1. PIN CACHING SYSTEM:
 *    - Pins are cached for 2 minutes and only refetched when moving >50m
 *    - Cache is automatically cleaned when it grows >100 entries
 *    - Real pin data is stored in _pinCache with metadata
 * 
 * 2. CLUSTER CACHING:
 *    - Clusters are cached by zoom level for instant switching
 *    - Cache is cleaned to keep only last 15-20 zoom levels
 *    - Prevents recalculating clusters on small camera movements
 * 
 * 3. DEBOUNCED UPDATES:
 *    - Camera position changes debounced to 800ms (was 500ms)
 *    - Zoom threshold increased to 0.8 (was 0.5) for cluster switching
 *    - Aura updates debounced to 500ms to reduce calculations
 * 
 * 4. PERFORMANCE IMPROVEMENTS:
 *    - Pin bounce animation reduced to 100ms intervals (was 50ms)
 *    - Glow animation reduced to 300ms intervals (was 200ms)
 *    - Playing pin glow reduced to 200ms intervals (was 100ms)
 *    - Concurrent symbol updates using Future.wait()
 *    - Reduced glow layers from 4 to 2 for better performance
 * 
 * 5. SMART REBUILDING:
 *    - Only rebuilds when switching between clustered/individual views
 *    - Tracks current display state to prevent unnecessary rebuilds
 *    - Concurrent fetch prevention with _isPinFetchInProgress flag
 * 
 * 6. REDUCED LOGGING:
 *    - Silent error handling where appropriate
 *    - Performance timing logs for monitoring
 *    - Cache statistics logging
 * 
 * Result: Pins no longer flicker and clusters appear instantly while maintaining
 * smooth animations and responsive user interactions.
 * 
 * FIXED: Progressive rendering layer switching issue where pins would disappear
 * when zooming out during progressive rendering. Now defers layer switching
 * until progressive rendering completes to prevent conflicts.
 */

class SnapchatStyleMapScreen extends StatefulWidget {
  final bool showBottomNav;
  final VoidCallback? onZoomIn;
  final VoidCallback? onZoomOut;
  final VoidCallback? onToggle3DBuildings;
  final VoidCallback? onToggleVegetation;
  final VoidCallback? onAdjustTilt;
  final VoidCallback? onToggleFollowUser;
  final bool? show3DBuildings;
  final bool? showVegetation;
  final bool? isFollowingUser;
  final ValueChanged<Map<String, dynamic>>? onSymbolTap;
  final Function(MaplibreMapController)? onMapCreated;

  // New callbacks for additional functionality
  final VoidCallback? onSettingsPressed;
  final VoidCallback? onSearchPressed;
  final VoidCallback? onAddPinPressed;
  final VoidCallback? onCommentPressed;
  final VoidCallback? onNotePressed;

  // New callbacks for pin highlighting
  final ValueChanged<String>? onPinHighlightStarted;
  final ValueChanged<String>? onPinHighlightStopped;

  // Callback for forwarding caption display to parent
  final Function(String, String)? onShowCaptionToParent;

  const SnapchatStyleMapScreen({
    super.key,
    this.showBottomNav = false,
    this.onZoomIn,
    this.onZoomOut,
    this.onToggle3DBuildings,
    this.onToggleVegetation,
    this.onAdjustTilt,
    this.onToggleFollowUser,
    this.show3DBuildings,
    this.showVegetation,
    this.isFollowingUser,
    this.onSymbolTap,
    this.onMapCreated,
    this.onSettingsPressed,
    this.onSearchPressed,
    this.onAddPinPressed,
    this.onCommentPressed,
    this.onNotePressed,
    this.onPinHighlightStarted,
    this.onPinHighlightStopped,
    this.onShowCaptionToParent,
  });

  @override
  State<SnapchatStyleMapScreen> createState() => SnapchatStyleMapScreenState();

  // Static method to access state for external control
  static SnapchatStyleMapScreenState? of(BuildContext context) {
    return context.findAncestorStateOfType<SnapchatStyleMapScreenState>();
  }
}

// Rendering context tracking for progressive rendering optimization
enum RenderingContext {
  initialLoad,
  mapStyleChange,
  filterChange,
  incrementalUpdate,
  userTriggered
}

class SnapchatStyleMapScreenState extends State<SnapchatStyleMapScreen>
    with TickerProviderStateMixin, WidgetsBindingObserver, RouteAware {
  /// Route observer to detect when this screen is not visible
  static final RouteObserver<PageRoute> routeObserver =
      RouteObserver<PageRoute>();

  /// Whether this screen is currently visible in the navigation stack
  bool _isCurrentlyOnScreen = true;
  MaplibreMapController? _mapController;
  late MapStyleManager _mapStyleManager;
  late VisualEffectsManager _visualEffectsManager;
  late WeatherVisualEffectsManager _weatherEffectsManager;
  late EnhancedGestureManager _gestureManager;
  late LayerStateMachine _layerStateMachine;
  late SmartLocationFetchManager _smartFetchManager;
  StreamSubscription<Position?>? _positionStreamSubscription;
  StreamSubscription<CompassEvent>? _compassStreamSubscription;
  Position? _currentPosition;
  double? _currentHeading;
  Symbol? _userLocationSymbol;
  Timer? _locationUpdateTimer;
  bool _isMapReady = false;
  bool _isMapFullyInitialized = false; // Track if all resources are loaded
  bool _shouldSnapToLocationOnFirstUpdate =
      false; // Flag to snap to location on first permission grant
  bool _needsLayerRebuild =
      false; // Track when layers need full rebuild vs visibility switch

  // Add MapProvider reference and pin tracking
  MapProvider? _mapProvider; // Will be assigned in didChangeDependencies
  int _prevPinCount = -1;

  // Add flag to track initial pin loading
  bool _isInitialPinLoad = true;

  // Filter state variables
  PinFilterType _currentFilter = PinFilterType.all;
  bool _isFilterDropdownOpen = false;
  bool _isLoadingPins = false;
  late AnimationController _filterAnimationController;
  late Animation<double> _filterAnimation;

  // Animation controllers (visual effects moved to VisualEffectsManager)
  late AnimationController _pulseAnimationController;
  late AnimationController _avatarBounceController;
  late AnimationController _pinGlowAnimationController;
  late AnimationController _radiusAnimationController;
  late AnimationController _userIconAnimationController;
  late AnimationController _pinBounceAnimationController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _avatarBounceAnimation;
  late Animation<double> _pinGlowAnimation;
  late Animation<double> _radiusAnimation;
  late Animation<double> _userIconAnimation;
  late Animation<double> _pinBounceAnimation;

  // GPS flashing animation
  late AnimationController _gpsFlashingController;
  late Animation<double> _gpsFlashingAnimation;
  late Animation<double> _gpsScaleAnimation;

  // Performance optimization
  static const double _minZoomFor3D = 15.0;
  static const double _defaultZoom = 16.5;
  static const double _defaultTilt = 45.0;
  static const double _defaultBearing = 0.0;

  // Test pins data
  List<Symbol> _testPinSymbols = [];
  List<Map<String, dynamic>> _testPinsData = [];
  Timer? _glowAnimationTimer;
  double _currentGlowRadius = 1.0;

  Timer? _pinBounceTimer;

  // Pin clustering state
  List<Symbol> _clusterSymbols = [];
  List<Map<String, dynamic>> _clusters = [];
  static const double _clusterDistance = 50.0; // pixels
  static const double _maxZoomForClustering = 14.0;
  // Track whether the current view is clustered or individual to avoid
  // unnecessary removal/re-adding of pins when the camera moves slightly.
  bool _isClusteredView = false;

  // Camera tracking for clustering
  double _currentZoom = _defaultZoom;
  double _currentMapBearing = _defaultBearing;

  // Current selected map style
  MapStyle _currentMapStyle =
      MapStyle.minimal; // Changed from MapStyle.standard to MapStyle.minimal

  // Style constants - Using Stadia Maps for OSM vector tiles
  static final String _stadiaApiKey =
      AppConstants.stadiaApiKey; // Replace with actual key

  // Map style configurations
  static final Map<MapStyle, MapStyleConfig> _mapStyles = {
    MapStyle.minimal: MapStyleConfig(
      name: 'Minimal',
      lightUrl:
          'https://tiles.stadiamaps.com/styles/osm_bright.json?api_key=$_stadiaApiKey',
      darkUrl:
          'https://tiles.stadiamaps.com/styles/alidade_smooth_dark.json?api_key=$_stadiaApiKey',
      icon: Icons.circle_outlined,
      accentColor: Colors.grey,
    ),
    MapStyle.standard: MapStyleConfig(
      name: 'Standard',
      lightUrl:
          'https://tiles.stadiamaps.com/styles/osm_bright.json?api_key=$_stadiaApiKey',
      darkUrl:
          'https://tiles.stadiamaps.com/styles/alidade_smooth_dark.json?api_key=$_stadiaApiKey',
      icon: Icons.map,
      accentColor: Colors.blue,
    ),
    MapStyle.neon: MapStyleConfig(
      name: 'Neon',
      lightUrl:
          'https://tiles.stadiamaps.com/styles/alidade_smooth_dark.json?api_key=$_stadiaApiKey',
      darkUrl:
          'https://tiles.stadiamaps.com/styles/alidade_smooth_dark.json?api_key=$_stadiaApiKey',
      icon: Icons.electric_bolt,
      accentColor: Colors.cyan,
    ),
    MapStyle.cyberpunk: MapStyleConfig(
      name: 'Cyberpunk',
      lightUrl:
          'https://tiles.stadiamaps.com/styles/alidade_smooth_dark.json?api_key=$_stadiaApiKey',
      darkUrl:
          'https://tiles.stadiamaps.com/styles/alidade_smooth_dark.json?api_key=$_stadiaApiKey',
      icon: Icons.computer,
      accentColor: Colors.purple,
    ),
    MapStyle.oldSchool: MapStyleConfig(
      name: 'Old School',
      lightUrl:
          'https://tiles.stadiamaps.com/styles/stamen_toner_lite.json?api_key=$_stadiaApiKey',
      darkUrl:
          'https://tiles.stadiamaps.com/styles/stamen_toner.json?api_key=$_stadiaApiKey',
      icon: Icons.history,
      accentColor: Colors.grey,
    ),
    MapStyle.watercolor: MapStyleConfig(
      name: 'Watercolor',
      lightUrl:
          'https://tiles.stadiamaps.com/styles/stamen_watercolor.json?api_key=$_stadiaApiKey',
      darkUrl:
          'https://tiles.stadiamaps.com/styles/stamen_watercolor.json?api_key=$_stadiaApiKey',
      icon: Icons.brush,
      accentColor: Colors.brown,
    ),
    MapStyle.retro: MapStyleConfig(
      name: 'Retro',
      lightUrl:
          'https://tiles.stadiamaps.com/styles/alidade_smooth.json?api_key=$_stadiaApiKey',
      darkUrl:
          'https://tiles.stadiamaps.com/styles/alidade_smooth.json?api_key=$_stadiaApiKey',
      icon: Icons.games,
      accentColor: Colors.pink,
    ),
    MapStyle.matrix: const MapStyleConfig(
      name: 'Matrix',
      lightUrl:
          'https://f005.backblazeb2.com/file/bopmaps/matrix.json',
      darkUrl:
          'https://f005.backblazeb2.com/file/bopmaps/matrix.json',
      icon: Icons.code,
      accentColor: Colors.green,
    ),
    MapStyle.vaporwave: MapStyleConfig(
      name: 'Vaporwave',
      lightUrl:
          'https://tiles.stadiamaps.com/styles/alidade_smooth_dark.json?api_key=$_stadiaApiKey',
      darkUrl:
          'https://tiles.stadiamaps.com/styles/alidade_smooth_dark.json?api_key=$_stadiaApiKey',
      icon: Icons.waves,
      accentColor: Colors.pinkAccent,
    ),
    MapStyle.futuristic: const MapStyleConfig(
      name: 'Futuristic',
      lightUrl:
          'https://f005.backblazeb2.com/file/bopmaps/matrix.json',
      darkUrl:
          'https://f005.backblazeb2.com/file/bopmaps/matrix.json',
      icon: Icons.grid_on,
      accentColor: Colors.cyan,
    ),
  };

  // Add this field to store real pin data
  final Map<String, Map<String, dynamic>> _realPinData = {};
  List<Map<String, dynamic>> _allPinsData = [];

  // Features for glow circles, stored to be animated
  List<Map<String, dynamic>> _glowFeatures = [];

  // Track pins user is currently within aura radius of
  List<Map<String, dynamic>> _pinsInAura = [];

  // Track currently playing pin for highlighting
  String? _currentlyPlayingPinId;
  AnimationController? _playingPinAnimationController;
  Animation<double>? _playingPinAnimation;
  Timer? _playingPinGlowTimer;

  // Add caption popup state
  final Map<String, bool> _visibleCaptionPopups = {};
  final Map<String, AnimationController> _captionAnimationControllers = {};
  final Map<String, Animation<double>> _captionAnimations = {};
  static const double _captionZoomThreshold =
      17.0; // Show captions when zoomed in close enough
  Timer? _captionUpdateTimer;

  // Add pin caching and optimization fields after the existing state variables (around line 150)

  // Add these new fields after _allPinsData

  // Pin caching and optimization
  final Map<String, Map<String, dynamic>> _pinCache =
      {}; // Cache all fetched pins
  DateTime? _lastPinFetch; // Track when we last fetched pins
  double? _lastFetchLat; // Last fetch location
  double? _lastFetchLng;
  static const Duration _pinCacheTimeout =
      Duration(minutes: 2); // Cache timeout
  static const double _refetchDistance = 50.0; // Refetch if moved 50m
  bool _isPinFetchInProgress = false; // Prevent concurrent fetches

  // Debouncing for API calls
  Timer? _apiCallDebounceTimer;
  Timer? _filterChangeDebounceTimer;
  Timer? _cameraPositionDebounceTimer;
  static const Duration _apiCallDebounceDelay = Duration(milliseconds: 500);
  static const Duration _filterChangeDebounceDelay =
      Duration(milliseconds: 300);
  static const Duration _cameraPositionDebounceDelay =
      Duration(milliseconds: 100);

  // Cluster cache for instant switching
  final Map<String, List<Map<String, dynamic>>> _clusterCache =
      {}; // Cache clusters by zoom level

  // Track current display state to prevent unnecessary rebuilds
  bool _isCurrentlyShowingClusters = false;
  double _lastClusteringZoom = -1;

  // Performance tracking
  int _pinUpdateCount = 0;
  DateTime? _lastPinUpdate;

  // Track which cluster icons have been created to avoid recreating them
  final Set<int> _createdClusterIcons = {};

  // Track pin state for debugging
  int _lastKnownPinCount = 0;
  int _lastKnownClusterCount = 0;
  Timer? _pinStateMonitor;

  // Scanning animation for pin fetching
  late AnimationController _scanningAnimationController;
  late Animation<double> _scanningAnimation;
  Timer? _scanningEffectTimer;
  bool _isScanningActive = false;

  // Layer-based approach for instant cluster/pin switching
  bool _layersInitialized = false;
  bool _currentlyShowingClusterLayer = false;
  bool _userAvatarImageAdded =
      false; // Track if user avatar image has been added to prevent duplicates

  // New pin animation system
  final Set<String> _previousPinIds = {}; // Track known pin IDs
  final Map<String, DateTime> _newPinTimestamps =
      {}; // Track when pins were added
  final List<AnimationController> _newPinAnimationControllers = [];
  Timer? _sparkleCleanupTimer;

  bool _isProgrammaticallyUpdating = false;

  // Rebuild counter to force map reload
  int _rebuildCounter = 0;

  // Progressive pin rendering state
  bool _isProgressiveRenderingEnabled = true;
  final Set<String> _renderedPinIds =
      {}; // Track which pins are already rendered
  Timer? _progressiveUpdateTimer;
  final List<Map<String, dynamic>> _pendingPins =
      []; // Pins waiting to be rendered
  bool? _pendingClusterDisplay =
      null; // Flag to show clusters after progressive rendering completes 3 pins at a time
  static const int _progressiveUpdateBatchSize = 10; // Render 5 pins at a time
  static const Duration _progressiveUpdateInterval =
      Duration(milliseconds: 200); // Update every 200ms

  // CRITICAL: Atomic flag to ensure only one progressive rendering process at a time
  bool _isProgressiveRenderingActive = false;

  // Flag to track when a style change is in progress to preserve pin layers
  bool _isStyleChangeInProgress = false;
  
  // Rendering context tracking for optimization
  RenderingContext _currentRenderingContext = RenderingContext.initialLoad;

  // Layer feature tracking for incremental updates
  final Map<String, List<Map<String, dynamic>>> _layerFeatures = {};

  // Track pins added from AR placement for animation
  final Set<String> _arPlacedPinIds = {};

  // Method to track AR placed pins
  void _trackARPlacedPin(String pinId) {
    _arPlacedPinIds.add(pinId);
    debugPrint('🎯 AR placed pin tracked: $pinId');
  }

  bool _isRebuildingClusters = false;

  // CRITICAL FIX: Prevent concurrent source updates that cause pin disappearing
  bool _isUpdatingIndividualSource = false;
  bool _isUpdatingClusterSource = false;
  final Map<String, Completer<void>> _sourceUpdateCompleters = {};

  // CRITICAL: Track when setGeoJsonSource should be completely avoided
  bool _shouldAvoidSourceUpdates = false;
  DateTime? _lastSourceUpdate;

  @override
  void initState() {
    super.initState();
    _mapStyleManager = MapStyleManager(context);
    _visualEffectsManager = VisualEffectsManager(
      vsync: this,
      onStateChanged: _onVisualEffectsStateChanged,
    );

    // Initialize isolate services for non-blocking computation
    ClusterService.instance.initialize().catchError((e) {
      debugPrint('⚠️ Failed to initialize cluster isolate: $e');
    });
    PinFeatureService.instance.initialize().catchError((e) {
      debugPrint('⚠️ Failed to initialize pin feature isolate: $e');
    });
    _weatherEffectsManager = WeatherVisualEffectsManager(
      vsync: this,
      onStateChanged: _onWeatherEffectsStateChanged,
    );
    _gestureManager = EnhancedGestureManager();
    _gestureManager.onGestureStateChanged = () {
      debugPrint('🎯 Gesture state changed: ${_gestureManager.currentGesture}');
    };
    _layerStateMachine = LayerStateMachine();
    _layerStateMachine.onStateChanged = (oldState, newState) {
      debugPrint('📍 Layer state changed: $oldState → $newState');
      setState(() {});
    };
    _layerStateMachine.onStateError = (state, error) {
      debugPrint('📍 ❌ Layer state error in $state: $error');
    };
    _layerStateMachine.onValidationFailed = (message) {
      debugPrint('📍 ⚠️ Layer validation failed: $message');
    };
    _layerStateMachine.initialize();

    // Initialize smart fetch manager
    _smartFetchManager = SmartLocationFetchManager(
      onPinsFetched: _onSmartFetchPinsReceived,
      onError: _onSmartFetchError,
      onFetchStarted: _onSmartFetchStarted,
      onFetchCompleted: _onSmartFetchCompleted,
    );

    // Register callbacks with MapProvider for external triggers (e.g., AR pin creation)
    // 🚀 CRITICAL FIX: Use Provider.of directly to match AR screen's access method
    debugPrint('🟡 [MAP-INIT] === REGISTERING CALLBACKS ===');

    final mapProviderForCallbacks =
        Provider.of<MapProvider>(context, listen: false);
    debugPrint(
        '🟡 [MAP-INIT] MapProvider via Provider.of: $mapProviderForCallbacks');
    debugPrint(
        '🟡 [MAP-INIT] MapProvider hashCode: ${mapProviderForCallbacks.hashCode}');
    debugPrint('🟡 [MAP-INIT] Local _mapProvider: $_mapProvider');
    debugPrint(
        '🟡 [MAP-INIT] Local _mapProvider hashCode: ${_mapProvider?.hashCode}');

    // Register callbacks on the SAME instance AR screen will use
    mapProviderForCallbacks.setForceRefreshCallback(_triggerForceRefresh);
    mapProviderForCallbacks.setOptimisticPinCallback(displayOptimisticPin);

    debugPrint('🟡 [MAP-INIT] ✅ Callbacks registered on Provider.of instance!');
    debugPrint('🟡 [MAP-INIT] === CALLBACK REGISTRATION COMPLETE ===');

    // Set up global event bus listeners for cross-screen communication
    _setupEventBusListeners();

    // Load saved map style and effects state, then initialize visual effects manager
    _loadSavedMapStyle();
    _loadSavedEffectsState();
    _visualEffectsManager.changeMapStyle(_currentMapStyle);
    _initializeAnimations();
    _initializeLocation();

    // Add WidgetsBindingObserver to detect app lifecycle
    WidgetsBinding.instance.addObserver(this);

    // Set up weather particle creation with screen size
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final size = MediaQuery.of(context).size;
        _visualEffectsManager.updateWithScreenSize(size);
        _weatherEffectsManager.updateWithScreenSize(size);
        debugPrint(
            '🌧️ Initial screen size update: ${size.width}x${size.height}');
      }
    });

    // Add WidgetsBindingObserver to detect app lifecycle
    WidgetsBinding.instance.addObserver(this);
  }

  String _getPinRarity(Map<String, dynamic> pinData) {
    final skinDetails = pinData['skin_details'] ?? pinData['skinDetails'];
    if (skinDetails is Map<String, dynamic>) {
      final metadata = skinDetails['metadata'];
      if (metadata is Map<String, dynamic>) {
        if (metadata.containsKey('rarity')) {
          return metadata['rarity']?.toString().toLowerCase() ?? 'common';
        }
      } else if (metadata is String && metadata.isNotEmpty) {
        try {
          final decoded = jsonDecode(metadata);
          if (decoded is Map<String, dynamic> &&
              decoded.containsKey('rarity')) {
            return decoded['rarity']?.toString().toLowerCase() ?? 'common';
          }
        } catch (e) {
          debugPrint('Could not decode metadata json: $e');
        }
      }
    }
    // Fallback to top-level rarity
    return pinData['rarity']?.toString().toLowerCase() ?? 'common';
  }

  // Track the previous black and white state to detect changes
  bool? _previousBlackAndWhiteState;

  // Track the previous theme state to detect theme changes
  bool? _previousThemeIsDarkMode;

  void _onVisualEffectsStateChanged() {
    if (mounted) {
      // Check if we're in retro mode and the black/white state changed
      if (_currentMapStyle == MapStyle.retro && _mapController != null) {
        final currentBWState = _visualEffectsManager.isBlackAndWhiteActive;

        if (_previousBlackAndWhiteState != currentBWState) {
          debugPrint(
              '📺 B&W state changed: $_previousBlackAndWhiteState → $currentBWState');
          _previousBlackAndWhiteState = currentBWState;

          // Reapply retro effects with new color mode
          Future.delayed(const Duration(milliseconds: 100), () {
            if (mounted && _currentMapStyle == MapStyle.retro) {
              debugPrint(
                  '📺 Reapplying retro effects for ${currentBWState ? 'B&W' : 'color'} mode');
              _applyRetroEffects();
            }
          });
        }
      }

      setState(() {});
    }
  }

  void _onWeatherEffectsStateChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  void didUpdateWidget(SnapchatStyleMapScreen oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Handle state changes from parent
    if (oldWidget.show3DBuildings != widget.show3DBuildings) {
      _handle3DBuildingsToggle();
    }

    if (oldWidget.showVegetation != widget.showVegetation) {
      _toggleVegetation();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Register for route changes to detect navigation away from this screen
    final route = ModalRoute.of(context);
    if (route != null && route is PageRoute) {
      routeObserver.subscribe(this, route);
    }

    // Handle MapProvider updates
    final mp = Provider.of<MapProvider>(context);
    if (_mapProvider != mp) {
      // Remove previous listener if it exists
      _mapProvider?.removeListener(_onMapProviderUpdate);
      _mapProvider = mp;
      _prevPinCount = _mapProvider!.pins.length;
      _mapProvider!.addListener(_onMapProviderUpdate);

      // Sync current filter with MapProvider and smart fetch manager
      if (_mapProvider!.currentFilter != _currentFilter) {
        _mapProvider!.setCurrentFilter(_currentFilter);
      }
      _smartFetchManager.setFilter(_currentFilter);
    }
  }

  // Listener callback to refresh map pins when MapProvider updates
  void _onMapProviderUpdate() async {
    if (!_isMapReady) {
      debugPrint('📍 MapProvider update ignored - map not ready');
      return;
    }

    setState(() {
      _isProgrammaticallyUpdating = true;
    });

    final currentCount = _mapProvider?.pins.length ?? 0;
    if (currentCount != _prevPinCount) {
      final previousCount = _prevPinCount;
      _prevPinCount = currentCount;

      debugPrint('📍 ===== MAP PROVIDER UPDATE =====');
      debugPrint('📍 Pin count: ${previousCount} → ${currentCount}');

      // Detect truly new pins for animation
      final newPinIds = _detectNewPins();

      debugPrint(
          '📍 New pins detected: ${newPinIds.length} [${newPinIds.join(", ")}]');

      // Check if these are AR placed pins (when MapProvider has add pin intent)
      if (newPinIds.isNotEmpty && _mapProvider?.hasAddPinIntent == true) {
        debugPrint(
            '📍 AR placement detected - tracking ${newPinIds.length} new pins');
        for (final pinId in newPinIds) {
          _trackARPlacedPin(pinId);
        }
      }

      // Sync fresh provider pins into the local cache so they appear immediately
      _syncCacheWithProviderPins();
      debugPrint('📍 Synced ${_mapProvider?.pins.length ?? 0} pins to cache');

      // Only clear cache if we don't have new pins to preserve, or if count decreased
      if (newPinIds.isEmpty || currentCount < previousCount) {
        _clearPinCache(force: true);
        debugPrint('📍 Cleared cache (no new pins or count decreased)');
      } else {
        // For new pins, preserve cache and force layer rebuild
        debugPrint(
            '📍 Preserving cache with new pins: ${newPinIds.join(", ")}');
        // Force layer rebuild to ensure new pins are rendered
        _layersInitialized = false;
      }

      // If we have new pins, force complete layer recreation
      if (newPinIds.isNotEmpty) {
        debugPrint('📍 New pins detected - forcing complete layer recreation');
        // Force layer recreation by resetting the flag
        _layersInitialized = false;
        _currentlyShowingClusterLayer = false;
        _isCurrentlyShowingClusters = false;

        // Clear existing layer state
        // await _clearExistingSymbols();
      }

      // Re-fetch and render pins with new data
      await _updateMapPins();

      debugPrint('📍 Pin rendering completed');

      // Force proper layer visibility state after rebuild
      _ensureCorrectLayerVisibility();

      // Small delay to ensure layers are properly initialized
      await Future.delayed(const Duration(milliseconds: 100));

      // If zoomed out, zoom in to see the new pin clearly
      if (_currentZoom < _maxZoomForClustering &&
          _mapController != null &&
          _mapProvider != null) {
        debugPrint(
            '📍 Zooming in to show new pin (current zoom: $_currentZoom)');

        // Find the first new pin to zoom to
        for (final pinId in newPinIds) {
          final pin = _pinCache[pinId] ?? _realPinData[pinId];
          if (pin != null &&
              pin['latitude'] != null &&
              pin['longitude'] != null) {
            // Zoom to the new pin location - this will trigger _onCameraPositionChanged
            // which will handle the layer switching automatically
            _mapController!.animateCamera(
              CameraUpdate.newLatLngZoom(
                LatLng(pin['latitude'], pin['longitude']),
                17.0, // Zoom level that shows individual pins clearly
              ),
              duration: const Duration(milliseconds: 800),
            );

            // Don't manually set layer visibility here - let the camera change handler do it
            // This prevents state sync issues
            debugPrint(
                '📍 Camera will zoom to 17.0, layer switching will be handled by camera change');
            return; // Exit early to let camera change handle layer switching
          }
        }
      } else {
        // If not zooming, manually set the correct layer visibility for current zoom
        final shouldCluster = _currentZoom < _maxZoomForClustering;
        await _switchLayerVisibility(shouldCluster);
        _currentlyShowingClusterLayer = shouldCluster;
        _isCurrentlyShowingClusters = shouldCluster;
        debugPrint(
            '📍 Set clustering state for current zoom: shouldCluster=$shouldCluster');
      }

      // Animate new pins immediately after they're rendered
      if (newPinIds.isNotEmpty) {
        debugPrint('📍 Starting animations for new pins immediately');
        // No delay - animate immediately
        _animateNewPins(newPinIds);
      }
    }

    // Check if map reload is needed (e.g., after returning from AR screen)
    if (_mapProvider!.needsMapReload) {
      debugPrint('🔄 Map reload requested by MapProvider');
      reloadMapStyle();
      _mapProvider!.clearMapReloadFlag();
    }

    setState(() {
      _isProgrammaticallyUpdating = false;
    });
  }

  // ===================== NEW HELPERS =====================
  // Sync filtered pins into cache for proper visual effects rendering
  void _syncFilteredPinsToCache(List<Map<String, dynamic>> filteredPins) {
    debugPrint('🔄 ===== SYNCING FILTERED PINS TO CACHE =====');
    debugPrint('🔄 Syncing ${filteredPins.length} filtered pins to cache');

    _pinCache.clear(); // Clear old cache

    for (final pinData in filteredPins) {
      final pinId = pinData['id']?.toString();
      if (pinId == null || pinId.isEmpty) {
        debugPrint('🔄 Skipping pin with null/empty ID');
        continue;
      }

      // Add to cache with proper metadata and coordinate extraction
      final cachedPin = Map<String, dynamic>.from(pinData);
      cachedPin['cached_at'] = DateTime.now().toIso8601String();
      cachedPin['from_filter'] = true;

      // Ensure coordinates are properly extracted
      if (cachedPin['latitude'] == null || cachedPin['longitude'] == null) {
        final location = cachedPin['location'];
        if (location != null) {
          if (location is String && location.contains('POINT')) {
            // Parse SRID=4326;POINT (lng lat) format - PostGIS format
            debugPrint('🔄 Parsing PostGIS location string: "$location"');
            try {
              final coordsStr = location.split('POINT ')[1].trim();
              final coords = coordsStr
                  .replaceAll('(', '')
                  .replaceAll(')', '')
                  .split(' ')
                  .map((s) => double.parse(s))
                  .toList();
              final longitude = coords[0];
              final latitude = coords[1];

              cachedPin['latitude'] = latitude;
              cachedPin['longitude'] = longitude;
              debugPrint(
                  '🔄 ✅ Extracted PostGIS coordinates for pin $pinId: lat=$latitude, lng=$longitude');
            } catch (e) {
              debugPrint(
                  '🔄 ❌ Failed to parse PostGIS coordinates for pin $pinId: $e');
            }
          } else if (location is Map<String, dynamic> &&
              location['type'] == 'Point' &&
              location['coordinates'] != null) {
            // Parse GeoJSON format: {type: "Point", coordinates: [lng, lat]}
            debugPrint('🔄 Parsing GeoJSON location: $location');
            try {
              final coordinates = location['coordinates'] as List;
              final longitude = (coordinates[0] as num).toDouble();
              final latitude = (coordinates[1] as num).toDouble();

              cachedPin['latitude'] = latitude;
              cachedPin['longitude'] = longitude;
              debugPrint(
                  '🔄 ✅ Extracted GeoJSON coordinates for pin $pinId: lat=$latitude, lng=$longitude');
            } catch (e) {
              debugPrint(
                  '🔄 ❌ Failed to parse GeoJSON coordinates for pin $pinId: $e');
            }
          } else {
            debugPrint(
                '🔄 ❌ Unknown location format: location=$location (type: ${location.runtimeType})');
          }
        } else {
          debugPrint('🔄 ❌ No location data available for pin $pinId');
        }
      } else {
        debugPrint(
            '🔄 Pin $pinId already has coordinates: lat=${cachedPin['latitude']}, lng=${cachedPin['longitude']}');
      }

      // Skip pins that still don't have valid coordinates after extraction
      if (cachedPin['latitude'] == null || cachedPin['longitude'] == null) {
        debugPrint(
            '🔄 ⚠️ Skipping pin $pinId - no valid coordinates after extraction');
        continue;
      }

      _pinCache[pinId] = cachedPin;
      _realPinData[pinId] = cachedPin;

      debugPrint(
          '🔄 ✅ Cached filtered pin: $pinId (lat=${cachedPin['latitude']}, lng=${cachedPin['longitude']})');
    }

    debugPrint('🔄 Final cache size: ${_pinCache.length}');
    debugPrint('🔄 ===== FILTER SYNC COMPLETE =====');
  }

  // Ensure that pins coming from MapProvider are present in the local
  // cache so that they can be rendered without waiting for the next API
  // fetch cycle.
  void _syncCacheWithProviderPins() {
    if (_mapProvider == null) {
      debugPrint('🔄 Sync skipped - no MapProvider');
      return;
    }

    // Skip syncing during filter changes to preserve filtered cache
    if (_currentRenderingContext == RenderingContext.filterChange) {
      debugPrint('🔄 Sync skipped - filter change in progress, preserving filtered cache');
      return;
    }

    debugPrint('🔄 ===== SYNCING CACHE WITH PROVIDER =====');
    debugPrint('🔄 Current cache size: ${_pinCache.length}');
    debugPrint('🔄 Provider pins: ${_mapProvider!.pins.length}');

    int newPinsAdded = 0;
    int existingPinsUpdated = 0;

    for (final dynamic pin in _mapProvider!.pins) {
      String? pinId;
      Map<String, dynamic>? pinMap;

      if (pin is Map<String, dynamic>) {
        pinId = pin['id']?.toString();
        pinMap = Map<String, dynamic>.from(pin); // Create a copy
        debugPrint('🔄 Processing map pin: $pinId');
      } else {
        try {
          // Attempt to use reflection-like access for common fields
          pinId = pin.id?.toString();
          pinMap = {
            'id': pin.id,
            'latitude': pin.latitude,
            'longitude': pin.longitude,
            'title': pin.title ?? '',
            'track_title': pin.trackTitle ?? '',
            'track_artist': pin.trackArtist ?? '',
            'caption': pin.caption ?? '',
            'rarity': pin.rarity ?? 'common',
            'aura_radius': pin.auraRadius ?? 25.0,
            'created_at': pin.createdAt?.toIso8601String() ??
                DateTime.now().toIso8601String(),
            'artwork_url': pin.artworkUrl ?? '',
            'owner': {
              'username': pin.owner?.username ?? 'Unknown',
              'name': pin.owner?.username ?? 'Unknown',
              'profile_pic': pin.owner?.profilePicUrl,
            },
            'skinDetails': pin.skinDetails,
            'service': pin.service,
            'upvote_count': pin.upvoteCount,
            'downvote_count': pin.downvoteCount,
            // Add cache metadata
            'cached_at': DateTime.now().toIso8601String(),
            'synced_from_provider': true,
          };
          debugPrint('🔄 Processing object pin: $pinId');
        } catch (e) {
          debugPrint('🔄 Failed to process pin object: $e');
          continue; // Skip if we can't derive id
        }
      }

      if (pinId == null || pinId.isEmpty) {
        debugPrint('🔄 Skipping pin with null/empty ID');
        continue;
      }

      // Filter out pins with null coordinates to prevent crashes
      if (pinMap != null &&
          (pinMap['latitude'] == null || pinMap['longitude'] == null)) {
        debugPrint(
            '🔄 Skipping pin from provider with null coordinates: $pinId');
        continue;
      }

      if (!_pinCache.containsKey(pinId)) {
        _pinCache[pinId] = pinMap ?? {};
        newPinsAdded++;
        debugPrint('🔄 ✅ Added new pin to cache: $pinId');
      } else {
        // Update existing pin with fresh data
        _pinCache[pinId] = pinMap ?? {};
        existingPinsUpdated++;
        debugPrint('🔄 📝 Updated existing pin in cache: $pinId');
      }

      // Also update realPinData for tap handling
      _realPinData[pinId] = _pinCache[pinId]!;
    }

    debugPrint(
        '🔄 Sync complete: +$newPinsAdded new, ~$existingPinsUpdated updated');
    debugPrint('🔄 Final cache size: ${_pinCache.length}');
    debugPrint('🔄 Final realPinData size: ${_realPinData.length}');
    debugPrint('🔄 ===== SYNC COMPLETE =====');
  }

  void _initializeAnimations() {
    // Pulse animation for user location
    _pulseAnimationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    _pulseAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _pulseAnimationController,
      curve: Curves.easeInOut,
    ));

    // Bounce animation for avatar
    _avatarBounceController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _avatarBounceAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _avatarBounceController,
      curve: Curves.elasticOut,
    ));

    // Glow animation for pins
    _pinGlowAnimationController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat(reverse: true);

    _pinGlowAnimation = Tween<double>(
      begin: 0.3,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _pinGlowAnimationController,
      curve: Curves.easeInOut,
    ));

    // Weather effects are now handled by VisualEffectsManager

    // Radius animation controller for pin auras
    _radiusAnimationController = AnimationController(
      duration:
          const Duration(seconds: 6), // Slower animation for more subtle effect
      vsync: this,
    )..repeat();

    _radiusAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _radiusAnimationController,
      curve: Curves.easeInOut,
    ));

    // Enhanced user icon animation
    _userIconAnimationController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat();

    _userIconAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _userIconAnimationController,
      curve: Curves.easeInOut,
    ));

    // Pin bouncing animation
    _pinBounceAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat(reverse: true);

    _pinBounceAnimation = Tween<double>(
      begin: 1.35,
      end: 1.45,
    ).animate(CurvedAnimation(
      parent: _pinBounceAnimationController,
      curve: Curves.easeInOut,
    ));

    // GPS flashing animation
    _gpsFlashingController = AnimationController(
      duration: const Duration(milliseconds: 1600),
      vsync: this,
    )..repeat(reverse: true);

    _gpsFlashingAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _gpsFlashingController,
      curve: Curves.easeInOut,
    ));

    _gpsScaleAnimation = Tween<double>(
      begin: 0.9,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _gpsFlashingController,
      curve: Curves.elasticInOut,
    ));

    // Playing pin highlight animation
    _playingPinAnimationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _playingPinAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _playingPinAnimationController!,
      curve: Curves.easeInOut,
    ));

    // Force clear all pin caches on initialization
    _clearPinCache(force: true);

    // Initialize pin bounce animation timer
    _startPinBounceAnimation();

    // Scanning animation for pin detection
    _scanningAnimationController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _scanningAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scanningAnimationController,
      curve: Curves.linear,
    ));

    // Start pin state monitoring
    _startPinStateMonitoring();

    // Filter animation controller
    _filterAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _filterAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _filterAnimationController,
      curve: Curves.easeInOut,
    ));
  }

Future<void> _initializeLocation() async {
    try {
      // Check location permission
      final permission = await Geolocator.checkPermission();
      bool permissionJustGranted = false;

      if (permission == LocationPermission.denied) {
        final newPermission = await Geolocator.requestPermission();
        if (newPermission == LocationPermission.denied ||
            newPermission == LocationPermission.deniedForever) {
          // Notify smart fetch manager about permission denial
          _smartFetchManager.onLocationPermissionChanged(false);
          return;
        }
        permissionJustGranted = true;
        _shouldSnapToLocationOnFirstUpdate =
            true; // Set flag to snap to location on first update
        debugPrint(
            '📍 Location permission just granted - will snap to location');
      }

      // Notify smart fetch manager about permission grant
      _smartFetchManager.onLocationPermissionChanged(true);

       // Get initial position
      _currentPosition = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      
      // Start location stream for position updates
      _positionStreamSubscription = Geolocator.getPositionStream(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: 10, // Update every 10 meters
        ),
      ).listen((Position position) {
        // SIMPLE ACCURACY FILTER: Skip poor accuracy GPS readings
        const maxAccuracy = 25.0; // meters - reject readings with accuracy worse than 20m
        if (position.accuracy > maxAccuracy) {
          debugPrint('📍 Rejected low accuracy GPS (${position.accuracy.toStringAsFixed(1)}m > ${maxAccuracy}m)');
          return;
        }
        
        debugPrint('📍 Received position update: '
            'lat: ${position.latitude}, lng: ${position.longitude}, '
            'accuracy: ${position.accuracy}m, speed: ${position.speed}m/s');
        
        _updateUserLocation(position);
      });

      if (permissionJustGranted) {
        // If permission was just granted, actively get current location
        debugPrint(
            '📍 Permission just granted, actively getting current location');
        await _getCurrentLocationAndTriggerPinFetch();
      }

      // Initialize compass stream for heading data
      _initializeCompass();

      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      debugPrint('Error initializing location: $e');
    }
  }

  /// Actively get current location and trigger pin fetch after permission is granted
  Future<void> _getCurrentLocationAndTriggerPinFetch() async {
    try {
      debugPrint(
          '📍 Actively requesting current location for first-time permission grant');

      // Timeout after 10 seconds to avoid indefinite waiting
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      debugPrint(
          '📍 Successfully obtained location: ${position.latitude}, ${position.longitude}');

      // Update user location in the map
      _updateUserLocation(position);

      // Explicitly trigger SmartLocationFetchManager to fetch pins now that we have location
      await _smartFetchManager.handleLocationUpdate(position);

      debugPrint('📍 Pin fetch triggered after location permission grant');
    } catch (e) {
      debugPrint('❌ Error getting current location after permission grant: $e');
      // Don't throw - we don't want to break the initialization
      // The position stream will eventually provide location updates
    }
  }

  Future<void> _initializeCompass() async {
    try {
      // Check if compass is available
      if (await FlutterCompass.events?.isEmpty == true) {
        debugPrint('🧭 Compass not available on this device');
        return;
      }

      // Start compass stream
      _compassStreamSubscription =
          FlutterCompass.events?.listen((CompassEvent event) {
        if (event.heading != null) {
          _currentHeading = event.heading;

          // Update only rotation if position is available and symbol exists
          if (_currentPosition != null &&
              _mapController != null &&
              _isMapReady &&
              _userLocationSymbol != null) {
            _updateUserMarkerRotation(_currentHeading!);
          }
        }
      });

      debugPrint('🧭 Compass stream initialized');
    } catch (e) {
      debugPrint('Error initializing compass: $e');
    }
  }

  void _updateUserLocation(Position position) {
    _currentPosition = position;

    if (_mapController != null && _isMapReady) {
      final latLng = LatLng(position.latitude, position.longitude);

      // Update user marker
      _updateUserMarker(latLng);

      // Follow user if enabled OR if this is the first location update after permission grant
      if (widget.isFollowingUser == true ||
          _shouldSnapToLocationOnFirstUpdate) {
        _mapController!.animateCamera(
          CameraUpdate.newLatLng(latLng),
          duration: const Duration(milliseconds: 800),
        );

        // Reset the first-time snap flag after using it
        if (_shouldSnapToLocationOnFirstUpdate) {
          _shouldSnapToLocationOnFirstUpdate = false;
          debugPrint('📍 Snapped to user location on first permission grant');
        }
      }

      // Use smart fetch manager instead of direct API calls
      _smartFetchManager.handleLocationUpdate(position);

      // Update pins in aura
      _updatePinsInAura();
    }
  }

  // Smart fetch manager callback methods
  void _onSmartFetchPinsReceived(List<Pin> pins) {
    debugPrint('📍 SmartFetch: Received ${pins.length} pins from API');

    // Convert Pin objects to Map format for compatibility with existing code
    final pinMaps = pins
        .map((pin) => {
              'id': pin.id,
              'latitude': pin.latitude,
              'longitude': pin.longitude,
              'title': pin.title ?? '',
              'track_title': pin.trackTitle ?? '',
              'track_artist': pin.trackArtist ?? '',
              'caption': pin.caption ?? '',
              'rarity': pin.rarity ?? 'common',
              'aura_radius': pin.auraRadius ?? 25.0,
              'created_at': pin.createdAt?.toIso8601String() ??
                  DateTime.now().toIso8601String(),
              'artwork_url': pin.artworkUrl ?? '',
              'owner': {
                'username': pin.owner?.username ?? 'Unknown',
                'name': pin.owner?.username ?? 'Unknown',
                'profile_pic': pin.owner?.profilePicUrl,
              },
              'skinDetails': pin.skinDetails,
              'service': pin.service,
              'upvote_count': pin.upvoteCount,
              'downvote_count': pin.downvoteCount,
              // Add cache metadata
              'cached_at': DateTime.now().toIso8601String(),
              'from_smart_fetch': true,
            })
        .toList();

    // Update the pin cache with new data
    debugPrint('📍 SmartFetch: Updating cache with ${pinMaps.length} pins');
    debugPrint('📍 SmartFetch: Cache before clear: ${_pinCache.length} pins');

    _pinCache.clear();
    for (final pinData in pinMaps) {
      final pinId = pinData['id']?.toString();
      if (pinId != null && pinId.isNotEmpty) {
        _pinCache[pinId] = pinData;
        _realPinData[pinId] = pinData;
      }
    }

    debugPrint('📍 SmartFetch: Cache after update: ${_pinCache.length} pins');

    // Update the map display
    _updateMapPinsFromCache();
  }

  void _onSmartFetchError(String message) {
    debugPrint('📍 SmartFetch: Error - $message');
    // Show error snackbar to user
    if (mounted && !_isLoadingPins) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to load ${_getFilterName().toLowerCase()} pins: $message'),
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 4),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _onSmartFetchStarted() {
    debugPrint('📍 SmartFetch: Started fetching pins');
    _startScanningAnimation();
  }

  void _onSmartFetchCompleted() {
    debugPrint('📍 SmartFetch: Completed fetching pins');
    // Note: Scanning animation will be stopped in _onProgressiveRenderingComplete()
    // after all pins are fully rendered, not just when API call completes
  }

  /// Trigger force refresh of pins (called by MapProvider when external actions need pin update)
  void _triggerForceRefresh() {
    debugPrint(
        '📍 Map: Force refresh triggered by external action (e.g., AR pin creation)');

    if (_mapProvider?.currentPosition != null) {
      // Use SmartLocationFetchManager's forceRefresh method
      _smartFetchManager.forceRefresh(_mapProvider!.currentPosition!);
    } else {
      debugPrint(
          '📍 Map: Cannot force refresh - no current position available');
    }
  }

  /// Display pin optimistically with animation while waiting for backend update
  /// This provides immediate visual feedback for AR pin drops
  Future<void> displayOptimisticPin(Map<String, dynamic> pinData) async {
    debugPrint('🔥 [OPTIMISTIC] === STARTING OPTIMISTIC PIN DISPLAY ===');
    debugPrint('🔥 [OPTIMISTIC] Pin data received: ${pinData.keys.toList()}');
    debugPrint('🔥 [OPTIMISTIC] Map controller: $_mapController');
    debugPrint('🔥 [OPTIMISTIC] Map ready: $_isMapReady');

    if (_mapController == null) {
      debugPrint('🛑 [OPTIMISTIC] CRITICAL: Map controller is null!');
      return;
    }

    if (!_isMapReady) {
      debugPrint('🛑 [OPTIMISTIC] CRITICAL: Map is not ready!');
      return;
    }

    try {
      debugPrint('🎯 [OPTIMISTIC] Map is ready, processing pin data...');

      // Extract coordinates from pin data with detailed logging
      double? lat, lng;
      debugPrint('🗺️ [OPTIMISTIC] Location data: ${pinData['location']}');

      if (pinData['location'] != null &&
          pinData['location']['coordinates'] != null) {
        final coords = pinData['location']['coordinates'] as List;
        lng = coords[0] as double;
        lat = coords[1] as double;
        debugPrint(
            '🗺️ [OPTIMISTIC] Using GeoJSON coordinates: lat=$lat, lng=$lng');
      } else if (pinData['latitude'] != null && pinData['longitude'] != null) {
        lat = pinData['latitude'] as double;
        lng = pinData['longitude'] as double;
        debugPrint('🗺️ [OPTIMISTIC] Using direct lat/lng: lat=$lat, lng=$lng');
      }

      if (lat == null || lng == null) {
        debugPrint(
            '🛑 [OPTIMISTIC] CRITICAL: Invalid coordinates - lat: $lat, lng: $lng');
        debugPrint(
            '🛑 [OPTIMISTIC] Available pin data keys: ${pinData.keys.toList()}');
        return;
      }

      final pinPosition = LatLng(lat, lng);
      debugPrint('🎯 [OPTIMISTIC] Pin position created: $pinPosition');

      // Generate temporary ID for optimistic pin
      final tempPinId = 'temp_${DateTime.now().millisecondsSinceEpoch}';
      debugPrint('🎯 [OPTIMISTIC] Generated temp ID: ${pinData['id']}');

      // Create optimistic pin data with temporary ID and required fields
      final optimisticPin = Map<String, dynamic>.from(pinData);
      optimisticPin['id'] = tempPinId;
      optimisticPin['isOptimistic'] = true; // Mark as optimistic
      optimisticPin['latitude'] = lat; // Ensure direct access
      optimisticPin['longitude'] = lng; // Ensure direct access

      debugPrint(
          '🎯 [OPTIMISTIC] Optimistic pin created with keys: ${optimisticPin.keys.toList()}');

      // Add to cache immediately for instant display
      _pinCache[tempPinId] = optimisticPin;
      debugPrint(
          '🎯 [OPTIMISTIC] Added to cache, cache size: ${_pinCache.length}');

      // Create single pin display with animation
      debugPrint('🎯 [OPTIMISTIC] Starting pin layer creation...');
      await _addOptimisticPinWithAnimation([optimisticPin]);

      // Trigger dramatic drop-in animation
      debugPrint('🎯 [OPTIMISTIC] Starting drop animation...');
      await _createEnhancedPinDropAnimation(tempPinId, pinPosition);

      debugPrint(
          '🎯 ✅ [OPTIMISTIC] SUCCESS! Pin displayed at ($lat, $lng) with ID: $tempPinId');
      debugPrint('🔥 [OPTIMISTIC] === OPTIMISTIC PIN DISPLAY COMPLETE ===');
    } catch (e, stackTrace) {
      debugPrint('🛑 [OPTIMISTIC] CRITICAL ERROR: $e');
      debugPrint('🛑 [OPTIMISTIC] Stack trace: $stackTrace');
    }
  }

  /// Add optimistic pin to layers with immediate display
  Future<void> _addOptimisticPinWithAnimation(
      List<Map<String, dynamic>> optimisticPins) async {
    if (_mapController == null || optimisticPins.isEmpty) return;

    try {
      debugPrint(
          '🎯 Adding ${optimisticPins.length} optimistic pins to layers');

      // Add to individual pins layer immediately
      await _addPinBatchToIndividualLayer(optimisticPins);

      // Add glow effects for visual appeal
      await _addIncrementalGlowEffects(optimisticPins);

      // Add to clusters if needed
      if (_currentZoom < _maxZoomForClustering) {
        await _addPinBatchToClusters(optimisticPins);
      }

      // Update rendered pins tracking
      for (final pin in optimisticPins) {
        final pinId = pin['id']?.toString();
        if (pinId != null) {
          _renderedPinIds.add(pinId);
        }
      }

      // Ensure correct layer visibility
      final shouldCluster = _currentZoom < _maxZoomForClustering;
      await _switchLayerVisibility(shouldCluster);

      debugPrint('🎯 ✅ Optimistic pins added to layers successfully');
    } catch (e) {
      debugPrint('🎯 ❌ Error adding optimistic pins to layers: $e');
    }
  }

  /// Create enhanced pin drop animation with multiple effects
  Future<void> _createEnhancedPinDropAnimation(
      String pinId, LatLng position) async {
    if (_mapController == null) return;

    try {
      debugPrint('🎯 Creating enhanced drop animation for pin $pinId');

      // 1. Create dramatic starburst effect
      await _createStarburstAnimation(pinId, position);

      // 2. Add a bounce/scale effect using symbol animation
      await _createPinBounceAnimation(pinId, position);

      // 3. Add ripple effect for extra drama
      await _createRippleAnimation(pinId, position);

      debugPrint('🎯 ✅ Enhanced drop animation created for pin $pinId');
    } catch (e) {
      debugPrint('🎯 ❌ Error creating enhanced drop animation: $e');
    }
  }

  /// Create pin bounce animation for dramatic entry
  Future<void> _createPinBounceAnimation(String pinId, LatLng position) async {
    if (_mapController == null) return;

    final animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _newPinAnimationControllers.add(animationController);

    // Create bounce animation curve
    final bounceAnimation = Tween<double>(
      begin: 0.3,
      end: 1.4,
    ).animate(CurvedAnimation(
      parent: animationController,
      curve: Curves.elasticOut,
    ));

    // Use bounce animation for visual feedback
    animationController.addListener(() {
      final currentScale = bounceAnimation.value;
      // The bounce scale can be used for visual feedback
      // Pin size animation is handled by the layer system
      debugPrint(
          '🎯 Pin bounce animation progress: ${currentScale.toStringAsFixed(2)}');
    });

    // Clean up when animation completes
    animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        animationController.dispose();
        _newPinAnimationControllers.remove(animationController);
      }
    });

    // Start the bounce animation
    animationController.forward();
  }

  /// Update map pins from cache without making API calls
  Future<void> _updateMapPinsFromCache() async {
    if (_mapController == null || !_isMapReady) {
      debugPrint('📍 Map not ready for pin updates');
      return;
    }

    debugPrint('📍 Updating map pins from cache (${_pinCache.length} pins)');

    try {
      // Convert cache to list format expected by existing methods
      final pinDataList = _pinCache.values.toList();

      if (pinDataList.isEmpty) {
        debugPrint('📍 No pins in cache to display');
        return;
      }

      // Check if pins are already rendered to avoid restarting progressive rendering
      final alreadyRenderedCount = pinDataList
          .where((pin) => _renderedPinIds.contains(pin['id']?.toString()))
          .length;

      debugPrint('📍 🔍 Cache analysis: ${pinDataList.length} total pins, ${alreadyRenderedCount} already rendered');

      // If progressive rendering is already active, don't interfere
      if (_isProgressiveRenderingActive || _isPinFetchInProgress || _progressiveUpdateTimer?.isActive == true) {
        debugPrint('📍 ⚠️ Progressive rendering already active, skipping cache update to avoid conflicts');
        return;
      }

      if (alreadyRenderedCount >= pinDataList.length * 0.8) {
        debugPrint(
            '📍 Most pins already rendered (${alreadyRenderedCount}/${pinDataList.length}), checking for new pins to add');

        // Find new pins that haven't been rendered yet
        final newPins = pinDataList
            .where((pin) => !_renderedPinIds.contains(pin['id']?.toString()))
            .toList();

        if (newPins.isNotEmpty) {
          debugPrint(
              '📍 ✨ Found ${newPins.length} new pins to add directly (avoiding progressive rendering)');

          // Add new pins directly using the helper method
          await _addRemainingPinsDirectly(newPins);
        } else {
          debugPrint('📍 ✅ All pins already rendered, ensuring correct layer visibility');

          // Ensure correct layer visibility
          final shouldCluster = _currentZoom < _maxZoomForClustering;
          if (shouldCluster != _currentlyShowingClusterLayer) {
            await _switchLayerVisibility(shouldCluster);
            _currentlyShowingClusterLayer = shouldCluster;
            _isCurrentlyShowingClusters = shouldCluster;
          }
        }

        // Stop scanning animation since all pins are rendered (no progressive rendering needed)
        _stopScanningAnimation();
      } else {
        debugPrint(
            '📍 Using progressive rendering for ${pinDataList.length} cached pins (${alreadyRenderedCount} already rendered)');
        // Set rendering context for incremental update
        _currentRenderingContext = RenderingContext.incrementalUpdate;
        // Force all rendering through progressive rendering (no legacy batch rendering)
        await _updateMapPinsProgressively();
      }

      // Update pins in aura
      _updatePinsInAura();
    } catch (e) {
      debugPrint('📍 Error updating map pins from cache: $e');
    }
  }

  bool _isUpdatingUserMarker = false;
  Future<void> _updateUserMarker(LatLng position) async {
    if (_mapController == null) return;

    // Prevent concurrent user marker updates that could create duplicates
    if (_isUpdatingUserMarker) {
      debugPrint('🔄 User marker update already in progress, skipping');
      return;
    }

    _isUpdatingUserMarker = true;

    try {
      // Add user avatar image if not already added
      await _addUserAvatarImage();

      // Get compass heading for rotation
      double? heading = _currentHeading;

      // If no compass heading is available, use a slow rotation for visual testing
      if (heading == null) {
        // Create a slow rotation for visual testing (1 full rotation every 60 seconds)
        final now = DateTime.now().millisecondsSinceEpoch;
        heading = (now / 1000 * 6) % 360; // 6 degrees per second
      }

      // Get current map bearing (rotation) to compensate for map rotation
      final cameraPosition = _mapController!.cameraPosition;
      final mapBearing = cameraPosition?.bearing ?? 0.0;

      // Adjust compass heading to account for map rotation
      // This ensures the compass points to true north regardless of map rotation
      final adjustedHeading = (heading - mapBearing) % 360;

      const consistentSize = 0.8; // Fixed size instead of animated

      if (_userLocationSymbol != null) {
        // Update existing symbol with new position and rotation
        await _mapController!.updateSymbol(
          _userLocationSymbol!,
          SymbolOptions(
            geometry: position,
            iconImage: 'user-avatar',
            iconSize: consistentSize,
            iconAnchor: 'center',
            iconRotate:
                adjustedHeading, // Use adjusted heading that compensates for map rotation
          ),
        );
      } else {
        // Remove any existing user avatars to prevent duplicates
        await _removeAllUserAvatars();

        // Create new symbol if it doesn't exist
        _userLocationSymbol = await _mapController!.addSymbol(
          SymbolOptions(
            geometry: position,
            iconImage: 'user-avatar',
            iconSize: consistentSize,
            iconAnchor: 'center',
            iconRotate:
                adjustedHeading, // Use adjusted heading that compensates for map rotation
          ),
        );

        debugPrint('✅ Created new user avatar symbol');
      }

      // Trigger bounce animation only for new positions
      if (_userLocationSymbol != null) {
        _avatarBounceController.forward(from: 0.0);
      }
    } catch (e) {
      debugPrint('Error updating user marker: $e');
    } finally {
      _isUpdatingUserMarker = false;
    }
  }

  /// Remove all user avatar symbols to prevent duplicates
  Future<void> _removeAllUserAvatars() async {
    if (_mapController == null) return;

    try {
      // Get all symbols and remove any with user-avatar icon
      final symbols = await _mapController!.symbols;
      for (final symbol in symbols) {
        if (symbol.options.iconImage == 'user-avatar') {
          await _mapController!.removeSymbol(symbol);
          debugPrint('🗑️ Removed duplicate user avatar symbol');
        }
      }
    } catch (e) {
      debugPrint('Error removing user avatars: $e');
    }
  }

  // Remove all building layers to prevent z-fighting
  void _removeAllBuildingLayers() {
    if (_mapController == null) return;

    final buildingLayers = [
      // Default building layers
      'building-3d', 'building-base-soft', 'building-edge-blur',
      'building-3d-residential', 'building-3d-commercial',
      'building-3d-industrial',
      'building', 'building-outline',

      // Shadow and effect layers
      'building-labels', 'building-ground-shadows',
      'building-ambient-occlusion', 'building-atmosphere',
      'building-atmosphere-simple',

      // Style-specific building layers
      'neon-building-3d', 'building-neon-glow', 'building-neon-edges',
      'building-base-glow', 'building-ground-glow',
      'cyberpunk-buildings', 'retro-buildings', 'matrix-buildings',
      'vaporwave-buildings', 'minimal-buildings', 'oldschool-buildings',
      'oldschool-buildings-2d', 'watercolor-buildings',
      'futuristic-buildings', 'futuristic-building-edges',
      'futuristic-building-glow',
    ];

    for (final layer in buildingLayers) {
      _mapController!.removeLayer(layer).catchError((e) {
        // Layer might not exist, that's okay
      });
    }

    debugPrint('🏢 Removed all building layers to prevent z-fighting');
  }

  Future<void> _onMapCreated(MaplibreMapController controller) async {
    _mapController = controller;
    _mapStyleManager.setMapController(controller);
    debugPrint('🗺️ Map created successfully');

    // Add the vector tile source for building data if it doesn't exist
    await _mapStyleManager.ensureVectorSource();

    // Ensure pin symbols can overlap and receive tap events
    await controller.setSymbolIconAllowOverlap(true);
    await controller.setSymbolIconIgnorePlacement(true);
    debugPrint('🗺️ Symbol settings configured');

    // Register for symbol tap events from the controller
    controller.onSymbolTapped.add(_onSymbolTapped);
    debugPrint('🗺️ Symbol tap listener registered');

    // Add camera position listener for clustering updates
    controller.addListener(_onCameraPositionChanged);
    debugPrint('🗺️ Camera position listener added');

    // Register MapProvider callbacks for AR pin support
    _registerCallbacksAfterStyleChange();

    // Notify parent about the map controller
    widget.onMapCreated?.call(controller);

    // Add custom user avatar image
    // await _addUserAvatarImage();

    // Set initial camera position
    if (_currentPosition != null) {
      await controller.animateCamera(
        CameraUpdate.newLatLngZoom(
          LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
          _defaultZoom,
        ),
      );

      // Ensure current zoom is properly initialized
      _currentZoom = _defaultZoom;
      debugPrint('🗺️ Initial zoom set to: $_currentZoom');

      // Add initial user marker
      _updateUserMarker(
        LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
      );

      // Load pins asynchronously without blocking the UI
      // _updateMapPins(); // This is now called in onStyleLoaded to ensure map style is ready
    }

    // The map is now ready for interaction.
    if (mounted && !_isMapReady) {
      setState(() {
        _isMapReady = true;
      });

      // Automatically tilt to reveal 3D buildings after initial map creation
      Future.delayed(const Duration(milliseconds: 500), () {
        _autoTiltForBuildings();
      });
    }
  }

  // Ensure user avatar appears on top of all layers including buildings
  bool _isEnsureUserAvatarInProgress = false;
  Future<void> _ensureUserAvatarOnTop() async {
    if (_mapController == null || _currentPosition == null) return;

    // Prevent concurrent calls that create duplicate user icons
    if (_isEnsureUserAvatarInProgress) {
      debugPrint('🔄 _ensureUserAvatarOnTop already in progress, skipping');
      return;
    }

    _isEnsureUserAvatarInProgress = true;

    try {
      // Remove existing user location symbol
      if (_userLocationSymbol != null) {
        await _mapController!.removeSymbol(_userLocationSymbol!);
        _userLocationSymbol = null;
      }

      // Re-add user marker on top of everything
      await _updateUserMarker(
        LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
      );

      debugPrint('🔝 User avatar repositioned on top of all layers');
    } catch (e) {
      debugPrint('Error ensuring user avatar on top: $e');
    } finally {
      _isEnsureUserAvatarInProgress = false;
    }
  }

  Future<void> _addUserAvatarImage() async {
    if (_mapController == null) return;

    // Prevent duplicate avatar images
    if (_userAvatarImageAdded) {
      debugPrint('📍 User avatar image already added, skipping');
      return;
    }

    try {
      // Create an enhanced futuristic user avatar
      final recorder = ui.PictureRecorder();
      final canvas = Canvas(recorder);
      final size = 120.0; // Larger for more detail
      final center = Offset(size / 2, size / 2);
      final mainRadius = size / 2 - 10;

      // Draw outer glow effect
      final glowPaint = Paint()
        ..color = Colors.cyan.withOpacity(0.3)
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);
      canvas.drawCircle(center, mainRadius + 8, glowPaint);

      // Draw pulsing ring
      final ringPaint = Paint()
        ..color = Colors.cyan.withOpacity(0.6)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 3;
      canvas.drawCircle(center, mainRadius + 5, ringPaint);

      // Draw main avatar background with gradient
      final avatarGradient = RadialGradient(
        center: const Alignment(-0.3, -0.3),
        radius: 1.2,
        colors: [
          Colors.cyan.withOpacity(0.9),
          Colors.blue.withOpacity(0.8),
          Colors.indigo.withOpacity(0.9),
        ],
      );

      final avatarPaint = Paint()
        ..shader = avatarGradient.createShader(
          Rect.fromCircle(center: center, radius: mainRadius),
        );
      canvas.drawCircle(center, mainRadius, avatarPaint);

      // Draw inner highlight
      final highlightPaint = Paint()
        ..shader = RadialGradient(
          center: const Alignment(-0.4, -0.4),
          radius: 0.6,
          colors: [
            Colors.white.withOpacity(0.5),
            Colors.white.withOpacity(0.1),
            Colors.transparent,
          ],
        ).createShader(Rect.fromCircle(center: center, radius: mainRadius));
      canvas.drawCircle(center, mainRadius, highlightPaint);

      // Draw user icon (person silhouette)
      final iconSize = mainRadius * 0.6;
      final iconPaint = Paint()
        ..color = Colors.white.withOpacity(0.9)
        ..style = PaintingStyle.fill;

      // Head
      canvas.drawCircle(
        Offset(center.dx, center.dy - iconSize * 0.3),
        iconSize * 0.25,
        iconPaint,
      );

      // Body (simplified)
      final bodyPath = Path();
      bodyPath.addOval(Rect.fromCenter(
        center: Offset(center.dx, center.dy + iconSize * 0.15),
        width: iconSize * 0.8,
        height: iconSize * 0.6,
      ));
      canvas.drawPath(bodyPath, iconPaint);

      // Enhanced direction indicator (arrow pointing north)
      // Draw shadow for the arrow first
      final arrowShadowPaint = Paint()
        ..color = Colors.black.withOpacity(0.3)
        ..style = PaintingStyle.fill;

      final arrowShadowPath = Path()
        ..moveTo(center.dx + 1, center.dy - mainRadius + 6)
        ..lineTo(center.dx - 5, center.dy - mainRadius + 16)
        ..lineTo(center.dx + 7, center.dy - mainRadius + 16)
        ..close();
      canvas.drawPath(arrowShadowPath, arrowShadowPaint);

      // Main arrow with gradient
      final arrowPaint = Paint()
        ..shader = LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.white,
            Colors.white.withOpacity(0.9),
          ],
        ).createShader(
            Rect.fromLTWH(center.dx - 8, center.dy - mainRadius + 5, 16, 12))
        ..style = PaintingStyle.fill;

      final arrowPath = Path()
        ..moveTo(center.dx, center.dy - mainRadius + 3) // Tip of arrow
        ..lineTo(center.dx - 7, center.dy - mainRadius + 15) // Left side
        ..lineTo(center.dx - 3, center.dy - mainRadius + 13) // Left inner
        ..lineTo(center.dx - 3, center.dy - mainRadius + 18) // Left stem
        ..lineTo(center.dx + 3, center.dy - mainRadius + 18) // Right stem
        ..lineTo(center.dx + 3, center.dy - mainRadius + 13) // Right inner
        ..lineTo(center.dx + 7, center.dy - mainRadius + 15) // Right side
        ..close();
      canvas.drawPath(arrowPath, arrowPaint);

      // Add a bright accent dot at the tip for better visibility
      final dotPaint = Paint()
        ..color = Colors.cyan
        ..style = PaintingStyle.fill;
      canvas.drawCircle(
          Offset(center.dx, center.dy - mainRadius + 3), 2, dotPaint);

      // Draw border with glassmorphism effect
      final borderPaint = Paint()
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2
        ..shader = LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withOpacity(0.8),
            Colors.white.withOpacity(0.3),
            Colors.white.withOpacity(0.6),
          ],
        ).createShader(Rect.fromCircle(center: center, radius: mainRadius));

      canvas.drawCircle(center, mainRadius, borderPaint);

      // Add status indicator dots
      final statusColors = [Colors.green, Colors.orange, Colors.red];
      for (int i = 0; i < 3; i++) {
        final angle = (i * 120) * (math.pi / 180);
        final dotCenter = Offset(
          center.dx + math.cos(angle) * (mainRadius + 12),
          center.dy + math.sin(angle) * (mainRadius + 12),
        );

        final dotPaint = Paint()..color = statusColors[i].withOpacity(0.8);
        canvas.drawCircle(dotCenter, 3, dotPaint);
      }

      // Convert to image
      final picture = recorder.endRecording();
      final image = await picture.toImage(size.toInt(), size.toInt());
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final bytes = byteData!.buffer.asUint8List();

      // Add to map
      await _mapController!.addImage('user-avatar', bytes);
      _userAvatarImageAdded = true; // Mark as added to prevent duplicates
      debugPrint('🎨 Enhanced user avatar added successfully');
    } catch (e) {
      debugPrint('Error adding enhanced user avatar: $e');
    }
  }

  Future<void> _updateUserMarkerRotation(double heading) async {
    if (_mapController == null || _userLocationSymbol == null) return;

    try {
      final consistentSize = 1.0; // Fixed size instead of animated

      // Get current map bearing (rotation) to compensate for map rotation
      final cameraPosition = _mapController!.cameraPosition;
      final mapBearing = cameraPosition?.bearing ?? 0.0;

      // Adjust compass heading to account for map rotation
      // This ensures the compass points to true north regardless of map rotation
      final adjustedHeading = (heading - mapBearing) % 360;

      // Update only the rotation and size of existing symbol
      await _mapController!.updateSymbol(
        _userLocationSymbol!,
        SymbolOptions(
          geometry: _userLocationSymbol!.options.geometry,
          iconImage: 'user-avatar',
          iconSize: consistentSize,
          iconAnchor: 'center',
          iconRotate: adjustedHeading,
        ),
      );
    } catch (e) {
      debugPrint('Error rotating user marker: $e');
    }
  }

  void _onStyleLoaded() async {
    if (_mapController == null) return;

    // When the style reloads (e.g. on theme change), the old symbol reference
    // becomes stale. Nullifying it here ensures that a new, valid symbol is
    // always created, preventing duplicate user icons from appearing.
    _userLocationSymbol = null;

    debugPrint('Map style loaded successfully');

    // CRITICAL: Check if this is a style change that should preserve pin layers
    final shouldPreservePinLayers = _isStyleChangeInProgress &&
                                   (_pinCache.isNotEmpty || _renderedPinIds.isNotEmpty);

    if (shouldPreservePinLayers) {
      debugPrint('🎨 ✅ Style change detected - preserving existing pin layers');
      // Only clean up style-specific layers, not pin layers
      _mapStyleManager.cleanupAllStyleLayers();

      // Ensure vector source is available - WAIT for it to complete
      await _mapStyleManager.ensureVectorSource();

      // Apply style-specific effects without touching pin layers
      await _applyStyleEffectsOnly();

      // Re-add user marker on top
      if (_currentPosition != null) {
        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted && _isMapReady) {
            _updateUserMarker(
              LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
            );
          }
        });
      }

      // Re-register callbacks
      _registerCallbacksAfterStyleChange();

      // Reset the flag
      _isStyleChangeInProgress = false;

      return; // Exit early to avoid pin layer rebuilding
    }

    // Original behavior for non-style-change scenarios
    // Clean up any existing style-specific layers first
    _mapStyleManager.cleanupAllStyleLayers();

    // Ensure vector source is available - WAIT for it to complete
    await _mapStyleManager.ensureVectorSource();

    // Check if the current style will provide its own building layers
    final stylesWithCustomBuildings = [
      MapStyle.neon,
      MapStyle.cyberpunk,
      MapStyle.retro,
      MapStyle.matrix,
      MapStyle.vaporwave,
      MapStyle.minimal,
      MapStyle.oldSchool,
      MapStyle.watercolor,
      MapStyle.futuristic,
    ];

    // Only add default building layers if the style doesn't have custom buildings
    if (widget.show3DBuildings == true &&
        !stylesWithCustomBuildings.contains(_currentMapStyle)) {
      // Add default 3D building layer after a short delay
      Future.delayed(const Duration(milliseconds: 500), () {
        _mapStyleManager.add3DBuildingLayer();
      });
    }

    // Customize map style for Snapchat look
    _customizeMapStyle();

    // Apply style-specific effects with a delay to ensure vector source is ready
    Future.delayed(const Duration(milliseconds: 500), () {
      switch (_currentMapStyle) {
        case MapStyle.neon:
          _mapStyleManager.applyNeonEffects();
          break;
        case MapStyle.cyberpunk:
          _mapStyleManager.applyCyberpunkEffects();
          break;
        case MapStyle.retro:
          _mapStyleManager.applyRetroEffects();
          break;
        case MapStyle.matrix:
          _mapStyleManager.applyMatrixEffects();
          break;
        case MapStyle.vaporwave:
          _mapStyleManager.applyVaporwaveEffects();
          break;
        case MapStyle.minimal:
          _mapStyleManager.applyMinimalEffects();
          break;
        case MapStyle.oldSchool:
          _mapStyleManager.applyOldSchoolEffects();
          break;
        case MapStyle.watercolor:
          _mapStyleManager.applyWatercolorEffects();
          break;
        case MapStyle.futuristic:
          _mapStyleManager.applyFuturisticEffects();
          break;
        default:
          // For standard style, apply buildings if 3D is enabled
          if (widget.show3DBuildings == true) {
            _mapStyleManager.add3DBuildingLayer();
          }
          break;
      }

      // Add all text and label layers LAST to ensure they appear on top
      Future.delayed(const Duration(milliseconds: 300), () {
        _addAllLabelsOnTop();

        // Re-add pins and user marker on top of everything after style change
        Future.delayed(const Duration(milliseconds: 200), () {
          if (mounted && _isMapReady) {
            // Force recreation of pin layers to ensure they're on top
            _layersInitialized = false;

            // Check if restoration process will handle pins
            final hasRestoreData =
                _pinCache.isNotEmpty || _renderedPinIds.isNotEmpty;

            if (hasRestoreData) {
              debugPrint(
                  '🎨 Using progressive rendering after style change (${_pinCache.length} cached pins)');
              // Set a flag to prevent restoration conflicts and add small delay for style to settle
              _isPinFetchInProgress = true;
              // Set rendering context for map style change
              _currentRenderingContext = RenderingContext.mapStyleChange;
              Timer(const Duration(milliseconds: 300), () {
                if (mounted && _mapController != null) {
                  _updateMapPinsProgressively();
                }
              });
            } else {
              // Fallback to filter application if no cache
              debugPrint('🎨 No cached pins, applying filter');
              _applyPinFilter();
            }

            // Ensure user avatar is on top of everything
            _ensureUserAvatarOnTop();

            // Additional check to ensure user location symbol is recreated after style change
            Future.delayed(const Duration(milliseconds: 300), () {
              if (mounted && _isMapReady && _currentPosition != null && _userLocationSymbol == null) {
                debugPrint('🔄 [STYLE-CHANGE] Final check: Recreating user location symbol');
                _updateUserMarker(
                  LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
                );
              }
            });

            // Restart scanning animation if it was active
            if (_isScanningActive) {
              _stopScanningAnimation();
              _startScanningAnimation();
            }
          }
        });
      });
    });

    // ---------------------------------------------------------------------
    // When the style is reloaded (e.g. when the user toggles between dark
    // and light themes) Maplibre clears all previously added runtime images,
    // symbols and layers.  We therefore need to re-add the runtime resources
    // (images) as well as the user-location marker and music pins so that
    // they remain visible after a style change or when the widget is
    // rebuilt after login/logout.
    // ---------------------------------------------------------------------

    // Re-register callbacks FIRST to ensure AR pins work
    _registerCallbacksAfterStyleChange();

    // Re-add runtime images (user avatar)
    // Pin icons will be re-inserted as part of _updateMapPins()
    _addUserAvatarImage();

    // Reset layer flags so they rebuild on the new style
    _layersInitialized = false;
    _currentlyShowingClusterLayer = false;

    // Clear cluster cache and reset clustering state on style change
    _clusterCache.clear();
    _createdClusterIcons.clear();
    _lastClusteringZoom = -1;
    _isCurrentlyShowingClusters = false;

    // CRITICAL FIX: DO NOT clear pin cache during style changes
    // This was causing pins to disappear when switching map styles
    // Pin data should be preserved across style changes
    debugPrint('🎨 ✅ Preserving pin cache during style change (${_pinCache.length} pins)');

    // CRITICAL FIX: DO NOT clear pin data during style changes
    // This was the main cause of pins disappearing during map style switches
    // Pin data should persist across style changes, only layers need rebuilding
    debugPrint('🎨 ✅ Preserving pin data during style change (${_allPinsData.length} pins)');

    // Re-add user marker if we already have a location - ensure it's on top
    if (_currentPosition != null) {
      // Small delay to ensure all other layers are added first
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted && _isMapReady) {
          _updateUserMarker(
            LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
          );
        }
      });
    }

    // CRITICAL FIX: Ensure user location symbol is recreated after all style operations
    // This addresses the issue where user location icon doesn't appear after style/theme changes
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted && _isMapReady && _currentPosition != null && _userLocationSymbol == null) {
        debugPrint('🔄 [STYLE-CHANGE] Ensuring user location symbol is recreated after style change');
        _updateUserMarker(
          LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
        );
      }
    });

    // Re-add pins (real pins will be fetched again inside this call).
    // This will also recreate glow circles and apply symbol overlap options.
    // Add a small delay to ensure style is fully loaded before adding pins
    Future.delayed(const Duration(milliseconds: 300), () async {
      if (mounted && _isMapReady) {
        _updateMapPins();
        if (!_isRebuildingClusters) {
          _isRebuildingClusters = true;
          try {
            await _rebuildClusterLayerWithAllPins();
          } catch (e) {
            debugPrint('📍 ⚠️ Error rebuilding cluster layer: $e');
          } finally {
            _isRebuildingClusters = false;
          }
        }
      }
    });

    // Mark map as fully initialized
    _isMapFullyInitialized = true;
    debugPrint('✅ Map fully initialized and ready');
  }

  /// Set up global event bus listeners for cross-screen communication
  void _setupEventBusListeners() {
    debugPrint('🌐 [EVENT-BUS] Setting up event bus listeners...');

    // Listen for optimistic pin display events from AR screen
    PinEventBus().optimisticPinStream.listen((pinData) {
      debugPrint('🌐 [EVENT-BUS] Received optimistic pin event: ${pinData['title']}');
      if (mounted) {
        displayOptimisticPin(pinData);
      }
    });

    // Listen for force refresh events from AR screen
    PinEventBus().forceRefreshStream.listen((_) {
      debugPrint('🌐 [EVENT-BUS] Received force refresh event');
      if (mounted) {
        _triggerForceRefresh();
      }
    });

    debugPrint('🌐 [EVENT-BUS] ✅ Event bus listeners set up successfully!');
  }

  /// Re-register callbacks with MapProvider after style change to ensure AR pins work
  /// Also ensures user location symbol is properly maintained
  void _registerCallbacksAfterStyleChange() {
    try {
      debugPrint('🔄 [STYLE-CHANGE] Re-registering callbacks after style change...');

      final mapProviderForCallbacks = Provider.of<MapProvider>(context, listen: false);

      // Re-register the optimistic pin callback for AR pins
      mapProviderForCallbacks.setOptimisticPinCallback(displayOptimisticPin);
      mapProviderForCallbacks.setForceRefreshCallback(_triggerForceRefresh);

      debugPrint('🔄 [STYLE-CHANGE] ✅ Callbacks re-registered successfully!');
      debugPrint('🔄 [STYLE-CHANGE] MapProvider hashCode: ${mapProviderForCallbacks.hashCode}');

      // Ensure user location symbol state is properly tracked
      if (_currentPosition != null && _userLocationSymbol == null) {
        debugPrint('🔄 [STYLE-CHANGE] User location symbol needs recreation after style change');
      }
    } catch (e) {
      debugPrint('🔄 [STYLE-CHANGE] ⚠️ Error re-registering callbacks: $e');
    }
  }

  void _startBuildingTiltAnimation() {
    // Automatically tilt to reveal 3D buildings after style loads
    _autoTiltForBuildings();
  }

  // Separate method for building labels to keep it organized
  void _addBuildingLabels() {
    if (_mapController == null) return;

    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    final isDarkMode = themeProvider.isDarkMode;

    // First add building icons (for special building types)
    _mapController!.addSymbolLayer(
      'openmaptiles',
      'building-icons',
      const SymbolLayerProperties(
        iconImage: [
          'case',
          [
            'in',
            ['get', 'building'],
            [
              'literal',
              ['hospital', 'clinic']
            ]
          ],
          'hospital-icon',
          [
            'in',
            ['get', 'building'],
            [
              'literal',
              ['school', 'university', 'college']
            ]
          ],
          'school-icon',
          [
            'in',
            ['get', 'building'],
            [
              'literal',
              ['church', 'cathedral', 'mosque', 'temple']
            ]
          ],
          'worship-icon',
          [
            'in',
            ['get', 'building'],
            [
              'literal',
              ['retail', 'shop', 'mall', 'commercial']
            ]
          ],
          'shop-icon',
          [
            'in',
            ['get', 'building'],
            [
              'literal',
              ['office', 'government']
            ]
          ],
          'office-icon',
          [
            'in',
            ['get', 'building'],
            [
              'literal',
              ['restaurant', 'cafe']
            ]
          ],
          'restaurant-icon',
          [
            'in',
            ['get', 'building'],
            [
              'literal',
              ['hotel', 'hostel']
            ]
          ],
          'hotel-icon',
          [
            'in',
            ['get', 'building'],
            [
              'literal',
              ['bank']
            ]
          ],
          'bank-icon',
          [
            'in',
            ['get', 'building'],
            [
              'literal',
              ['library']
            ]
          ],
          'library-icon',
          [
            'in',
            ['get', 'building'],
            [
              'literal',
              ['museum', 'gallery']
            ]
          ],
          'museum-icon',
          [
            'in',
            ['get', 'building'],
            [
              'literal',
              ['parking', 'garage']
            ]
          ],
          'sports-icon',
          'building-icon' // Default icon
        ],
        iconSize: [
          'interpolate',
          ['linear'],
          ['zoom'],
          16,
          0.5,
          17,
          0.7,
          18,
          0.9,
          19,
          1.1,
        ],
        iconAllowOverlap: false,
        iconIgnorePlacement: false,
        iconAnchor: 'bottom',
        symbolPlacement: 'point',
        symbolSpacing: 100,
      ),
      sourceLayer: 'building',
      minzoom: 16.0,
      filter: [
        'all',
        ['has', 'name'],
        [
          '>',
          ['get', 'render_height'],
          15
        ], // Show icons for taller buildings
      ],
    ).then((_) {
      debugPrint('🏢 Added building icons');
    }).catchError((e) {
      debugPrint('Could not add building icons: $e');
    });

    // Then add building labels with enhanced styling
    _mapController!.addSymbolLayer(
      'openmaptiles',
      'building-labels',
      SymbolLayerProperties(
        // Text field with proper formatting
        textField: [
          'format',
          [
            'upcase',
            ['get', 'name']
          ],
          {},
          '\n',
          {},
          [
            'case',
            ['has', 'addr:housenumber'],
            ['get', 'addr:housenumber'],
            ''
          ],
          {'font-scale': 0.8}
        ],
        // Using common fonts with fallbacks
        textFont: [
          'literal',
          ['Open Sans Bold', 'Arial Unicode MS Bold']
        ],
        textSize: [
          'interpolate',
          ['linear'],
          ['zoom'],
          16,
          10,
          17,
          12,
          18,
          14,
          19,
          16,
        ],
        textColor: isDarkMode ? '#FFFFFF' : '#2C3E50',
        textHaloColor: isDarkMode ? '#000000' : '#FFFFFF',
        textHaloWidth: 2.0,
        textHaloBlur: 0.5,
        textAnchor: 'top', // Place text above icon
        textOffset: [0, 0.5], // Slight offset down from anchor
        symbolPlacement: 'point',
        textAllowOverlap: false,
        textIgnorePlacement: false,
        symbolSpacing: 250,
        // Enhanced text styling
        textJustify: 'center',
        textMaxWidth: 10,
        textLineHeight: 1.2,
        // Make text more visible
        textOpacity: [
          'interpolate',
          ['linear'],
          ['zoom'],
          16,
          0.7,
          17,
          0.85,
          18,
          1.0,
        ],
      ),
      sourceLayer: 'building',
      minzoom: 16.0,
      // Enhanced filter - show labels for important/tall buildings
      filter: [
        'all',
        ['has', 'name'],
        [
          'any',
          [
            '>',
            ['get', 'render_height'],
            20
          ], // Tall buildings
          [
            'in',
            ['get', 'building'],
            [
              'literal',
              [
                'hospital',
                'school',
                'university',
                'church',
                'retail',
                'office',
                'government',
                'hotel',
                'museum',
                'library',
                'bank',
                'restaurant',
                'stadium',
                'mall',
                'commercial'
              ]
            ]
          ], // Important building types
        ],
      ],
    ).then((_) {
      debugPrint('🏷️ Added enhanced building labels');
    }).catchError((e) {
      debugPrint('Could not add building labels: $e');
    });

    // Add building type icons
    _addBuildingIcons();
  }

  // Add icons for different building types
  Future<void> _addBuildingIcons() async {
    if (_mapController == null) return;

    final iconTypes = {
      'hospital-icon': Icons.local_hospital,
      'school-icon': Icons.school,
      'worship-icon': Icons.church,
      'shop-icon': Icons.shopping_bag,
      'office-icon': Icons.business,
      'restaurant-icon': Icons.restaurant,
      'hotel-icon': Icons.hotel,
      'bank-icon': Icons.account_balance,
      'library-icon': Icons.local_library,
      'museum-icon': Icons.museum,
      'parking-icon': Icons.local_parking,
      'sports-icon': Icons.sports,
      'building-icon': Icons.apartment, // Default
    };

    for (final entry in iconTypes.entries) {
      try {
        final bytes = await _createBuildingIcon(entry.value);
        await _mapController!.addImage(entry.key, bytes);
      } catch (e) {
        debugPrint('Error adding ${entry.key}: $e');
      }
    }
  }

  // Add icons for different POI types
  Future<void> _addPOIIcons() async {
    if (_mapController == null) return;

    final poiIconTypes = {
      'poi-restaurant': Icons.restaurant,
      'poi-cafe': Icons.local_cafe,
      'poi-hotel': Icons.hotel,
      'poi-hospital': Icons.local_hospital,
      'poi-school': Icons.school,
      'poi-bank': Icons.account_balance,
      'poi-shop': Icons.shopping_bag,
      'poi-museum': Icons.museum,
      'poi-library': Icons.local_library,
      'poi-park': Icons.park,
      'poi-default': Icons.place, // Default POI icon
    };

    for (final entry in poiIconTypes.entries) {
      try {
        final bytes = await _createPOIIcon(entry.value, entry.key);
        await _mapController!.addImage(entry.key, bytes);
        debugPrint('✅ Added POI icon: ${entry.key}');
      } catch (e) {
        debugPrint('Error adding POI icon ${entry.key}: $e');
      }
    }
  }

  // Create building type icon
  Future<Uint8List> _createBuildingIcon(IconData icon) async {
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    final size = 48.0;

    // Background circle with glassmorphism effect
    final bgPaint = Paint()
      ..shader = RadialGradient(
        colors: [
          Colors.white.withOpacity(0.9),
          Colors.white.withOpacity(0.7),
        ],
      ).createShader(Rect.fromCircle(
        center: Offset(size / 2, size / 2),
        radius: size / 2,
      ));

    canvas.drawCircle(
      Offset(size / 2, size / 2),
      size / 2 - 2,
      bgPaint,
    );

    // Border
    canvas.drawCircle(
      Offset(size / 2, size / 2),
      size / 2 - 2,
      Paint()
        ..color = Colors.black.withOpacity(0.2)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1,
    );

    // Draw icon
    final textPainter = TextPainter(
      text: TextSpan(
        text: String.fromCharCode(icon.codePoint),
        style: TextStyle(
          fontFamily: icon.fontFamily,
          fontSize: size * 0.5,
          color: Colors.black87,
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        (size - textPainter.width) / 2,
        (size - textPainter.height) / 2,
      ),
    );

    final picture = recorder.endRecording();
    final image = await picture.toImage(size.toInt(), size.toInt());
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    return byteData!.buffer.asUint8List();
  }

  // Create POI type icon with distinct styling
  Future<Uint8List> _createPOIIcon(IconData icon, String iconKey) async {
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    final size = 64.0; // Larger than building icons
    final center = Offset(size / 2, size / 2);

    // Choose color based on POI type
    Color poiColor;
    switch (iconKey) {
      case 'poi-restaurant':
      case 'poi-cafe':
        poiColor = Colors.orange;
        break;
      case 'poi-hotel':
        poiColor = Colors.blue;
        break;
      case 'poi-hospital':
        poiColor = Colors.red;
        break;
      case 'poi-school':
        poiColor = Colors.green;
        break;
      case 'poi-bank':
        poiColor = Colors.purple;
        break;
      case 'poi-shop':
        poiColor = Colors.pink;
        break;
      case 'poi-museum':
      case 'poi-library':
        poiColor = Colors.brown;
        break;
      case 'poi-park':
        poiColor = Colors.lightGreen;
        break;
      default:
        poiColor = Colors.grey;
    }

    // Draw drop shadow
    final shadowPaint = Paint()
      ..color = Colors.black.withOpacity(0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4);
    canvas.drawCircle(center.translate(2, 2), size / 2 - 4, shadowPaint);

    // Draw outer glow
    final glowPaint = Paint()
      ..color = poiColor.withOpacity(0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);
    canvas.drawCircle(center, size / 2 - 2, glowPaint);

    // Draw main background with gradient
    final bgGradient = RadialGradient(
      center: const Alignment(-0.3, -0.3),
      radius: 1.2,
      colors: [
        Colors.white.withOpacity(0.95),
        Colors.white.withOpacity(0.8),
        poiColor.withOpacity(0.1),
      ],
    );

    final bgPaint = Paint()
      ..shader = bgGradient.createShader(
        Rect.fromCircle(center: center, radius: size / 2 - 4),
      );

    canvas.drawCircle(center, size / 2 - 4, bgPaint);

    // Draw colored border
    canvas.drawCircle(
      center,
      size / 2 - 4,
      Paint()
        ..color = poiColor
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2,
    );

    // Draw inner highlight
    final highlightPaint = Paint()
      ..shader = RadialGradient(
        center: const Alignment(-0.5, -0.5),
        radius: 0.8,
        colors: [
          Colors.white.withOpacity(0.6),
          Colors.white.withOpacity(0.1),
          Colors.transparent,
        ],
      ).createShader(Rect.fromCircle(center: center, radius: size / 3));

    canvas.drawCircle(center, size / 3, highlightPaint);

    // Draw icon
    final textPainter = TextPainter(
      text: TextSpan(
        text: String.fromCharCode(icon.codePoint),
        style: TextStyle(
          fontFamily: icon.fontFamily,
          fontSize: size * 0.4,
          color: poiColor.withOpacity(0.9),
          shadows: [
            Shadow(
              color: Colors.white.withOpacity(0.8),
              offset: const Offset(1, 1),
              blurRadius: 2,
            ),
          ],
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        (size - textPainter.width) / 2,
        (size - textPainter.height) / 2,
      ),
    );

    // Add a small indicator dot at the bottom for better ground alignment
    canvas.drawCircle(
      Offset(center.dx, size - 8),
      3,
      Paint()..color = poiColor.withOpacity(0.8),
    );

    final picture = recorder.endRecording();
    final image = await picture.toImage(size.toInt(), size.toInt());
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    return byteData!.buffer.asUint8List();
  }

  void _customizeMapStyle() {
    if (_mapController == null) return;

    debugPrint('Customizing map style for Snapchat look...');

    // Add custom layers for parks and vegetation
    _addParksAndVegetation();

    // Add tree symbols to parks
    _addTreeSymbols();

    // Enhance water features
    _enhanceWaterFeatures();

    // Only customize roads for non-watercolor styles (watercolor should use built-in roads)
    if (_currentMapStyle != MapStyle.watercolor) {
      _customizeRoads();
    }
  }

  void _addParksAndVegetation() {
    if (_mapController == null) return;

    try {
      // Remove existing park layers to replace with custom ones
      _mapController!.removeLayer('park-fill').catchError((e) {
        debugPrint('No existing park layer to remove');
      });

      // Add realistic park layer with natural green
      _mapController!
          .addFillLayer(
        'openmaptiles',
        'custom-parks',
        const FillLayerProperties(
          fillColor: '#B8E6B8', // Soft mint green with glassmorphism feel
          fillOpacity: [
            'interpolate',
            ['linear'],
            ['zoom'],
            10,
            0.4,
            14,
            0.6,
            18,
            0.8,
          ],
        ),
        sourceLayer: 'park',
        minzoom: 10.0,
      )
          .then((_) {
        debugPrint('Successfully added custom parks layer');
      }).catchError((e) {
        debugPrint('Error adding parks layer: $e');
      });

      // Add grass/meadow areas with realistic color
      _mapController!
          .addFillLayer(
        'openmaptiles',
        'custom-grass',
        const FillLayerProperties(
          fillColor: '#C8F0C8', // Light pastel green
          fillOpacity: 0.7,
        ),
        sourceLayer: 'landcover',
        filter: [
          'in',
          ['get', 'class'],
          [
            'literal',
            ['grass', 'meadow']
          ]
        ],
        minzoom: 11.0,
      )
          .then((_) {
        debugPrint('Successfully added grass layer');
      }).catchError((e) {
        debugPrint('Error adding grass layer: $e');
      });

      // Add forest/wood areas with darker realistic green
      _mapController!
          .addFillLayer(
        'openmaptiles',
        'custom-forest',
        const FillLayerProperties(
          fillColor: '#A4D4A4', // Softer forest green
          fillOpacity: 0.85,
        ),
        sourceLayer: 'landcover',
        filter: [
          'in',
          ['get', 'class'],
          [
            'literal',
            ['wood', 'forest']
          ]
        ],
        minzoom: 10.0,
      )
          .then((_) {
        debugPrint('Successfully added forest layer');
      }).catchError((e) {
        debugPrint('Error adding forest layer: $e');
      });

      // Add a subtle pattern overlay for visual interest
      _mapController!
          .addFillLayer(
        'openmaptiles',
        'park-overlay',
        const FillLayerProperties(
          fillColor: '#81C784',
          fillOpacity: [
            'interpolate',
            ['linear'],
            ['zoom'],
            14,
            0,
            16,
            0.2,
            18,
            0.3,
          ],
        ),
        sourceLayer: 'park',
        minzoom: 14.0,
      )
          .catchError((e) {
        debugPrint('Park overlay not added: $e');
      });
    } catch (e) {
      debugPrint('Error customizing vegetation: $e');
    }
  }

  void _addTreeSymbols() {
    if (_mapController == null) return;

    // Add tree symbols to parks at higher zoom levels
    try {
      // First add a simple tree icon
      _addTreeIcon();

      // Wait a bit for the icon to be added
      Future.delayed(const Duration(milliseconds: 300), () {
        if (_mapController == null) return;

        // Add scattered tree symbols to ALL parks
        _mapController!
            .addSymbolLayer(
          'openmaptiles',
          'park-trees',
          const SymbolLayerProperties(
            iconImage: 'tree-icon',
            iconSize: [
              'interpolate',
              ['linear'],
              ['zoom'],
              14, 0.4, // Visible from zoom 14
              15, 0.6,
              16, 0.8,
              17, 1.0,
              18, 1.2,
            ],
            iconAllowOverlap: false,
            symbolPlacement: 'point',
            symbolSpacing: 60, // Increased spacing for better tree distribution
            iconPadding: 8,
          ),
          sourceLayer: 'park',
          minzoom: 14.0, // Show from zoom 14
          // Remove the area filter to show trees in all parks
        )
            .then((_) {
          debugPrint('Successfully added tree symbols to parks');

          // Also add trees to landcover areas (grass, forest)
          _mapController!.addSymbolLayer(
            'openmaptiles',
            'landcover-trees',
            const SymbolLayerProperties(
              iconImage: 'tree-icon',
              iconSize: [
                'interpolate',
                ['linear'],
                ['zoom'],
                14,
                0.3,
                15,
                0.5,
                16,
                0.7,
                17,
                0.9,
              ],
              iconAllowOverlap: false,
              symbolPlacement: 'point',
              symbolSpacing: 80, // Even more spacing for landcover trees
              iconPadding: 10,
            ),
            sourceLayer: 'landcover',
            minzoom: 14.0,
            filter: [
              'in',
              ['get', 'class'],
              [
                'literal',
                ['grass', 'wood', 'forest']
              ]
            ],
          ).then((_) {
            debugPrint('Successfully added tree symbols to landcover');
          }).catchError((e) {
            debugPrint('Error adding landcover trees: $e');
          });
        }).catchError((e) {
          debugPrint('Error adding tree symbols: $e');
        });
      });
    } catch (e) {
      debugPrint('Error in tree symbols setup: $e');
    }
  }

  Future<void> _addTreeIcon() async {
    if (_mapController == null) return;

    try {
      // Create a more stylized tree icon with better contrast
      final recorder = ui.PictureRecorder();
      final canvas = Canvas(recorder);
      final size = 100.0; // Larger for better detail

      // Draw shadow with more presence
      final shadowPaint = Paint()
        ..color = Colors.black.withOpacity(0.4)
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 6);
      canvas.drawOval(
        Rect.fromCenter(
          center: Offset(size * 0.5, size * 0.92),
          width: size * 0.5,
          height: size * 0.15,
        ),
        shadowPaint,
      );

      // Draw tree trunk with gradient
      final trunkRect =
          Rect.fromLTWH(size * 0.43, size * 0.6, size * 0.14, size * 0.3);
      const trunkGradient = LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          const Color(0xFF8D6E63), // Light brown
          const Color(0xFF6D4C41), // Darker brown
        ],
      );
      canvas.drawRRect(
        RRect.fromRectAndRadius(trunkRect, const Radius.circular(3)),
        Paint()..shader = trunkGradient.createShader(trunkRect),
      );

      // Draw tree crown with glassmorphism effect
      final crownPaint = Paint()
        ..color = const Color(0xFF43A047) // Vibrant green
        ..style = PaintingStyle.fill;

      // Add outer glow
      final glowPaint = Paint()
        ..color = const Color(0xFF66BB6A).withOpacity(0.3)
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);

      // Bottom layer with glow
      canvas.drawCircle(
          Offset(size * 0.5, size * 0.45), size * 0.35, glowPaint);
      canvas.drawCircle(
          Offset(size * 0.5, size * 0.45), size * 0.3, crownPaint);

      // Middle layer
      canvas.drawCircle(
          Offset(size * 0.5, size * 0.35), size * 0.26, crownPaint);

      // Top layer
      canvas.drawCircle(
          Offset(size * 0.5, size * 0.25), size * 0.2, crownPaint);

      // Add glassmorphism highlights
      final highlightGradient = RadialGradient(
        center: const Alignment(-0.3, -0.3),
        radius: 0.8,
        colors: [
          Colors.white.withOpacity(0.4),
          Colors.white.withOpacity(0.1),
          Colors.transparent,
        ],
      );

      final highlightPaint = Paint()
        ..shader = highlightGradient.createShader(
          Rect.fromCircle(
              center: Offset(size * 0.5, size * 0.35), radius: size * 0.3),
        );

      canvas.drawCircle(
          Offset(size * 0.5, size * 0.35), size * 0.25, highlightPaint);

      // Add small detail highlights
      final detailPaint = Paint()
        ..color = const Color(0xFF81C784).withOpacity(0.8) // Lighter green
        ..style = PaintingStyle.fill;

      canvas.drawCircle(
          Offset(size * 0.4, size * 0.3), size * 0.06, detailPaint);
      canvas.drawCircle(
          Offset(size * 0.58, size * 0.38), size * 0.05, detailPaint);

      // Add edge definition
      final edgePaint = Paint()
        ..color = const Color(0xFF2E7D32).withOpacity(0.5)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.5;

      canvas.drawCircle(Offset(size * 0.5, size * 0.45), size * 0.3, edgePaint);
      canvas.drawCircle(
          Offset(size * 0.5, size * 0.35), size * 0.26, edgePaint);
      canvas.drawCircle(Offset(size * 0.5, size * 0.25), size * 0.2, edgePaint);

      // Convert to image
      final picture = recorder.endRecording();
      final image = await picture.toImage(size.toInt(), size.toInt());
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final bytes = byteData!.buffer.asUint8List();

      // Add to map
      await _mapController!.addImage('tree-icon', bytes);
      debugPrint('Successfully added Snapchat-style tree icon');
    } catch (e) {
      debugPrint('Error adding tree icon: $e');
    }
  }

  void _enhanceWaterFeatures() {
    if (_mapController == null) return;

    try {
      // Remove existing water layer to replace
      _mapController!.removeLayer('water').catchError((e) {
        debugPrint('No existing water layer to remove');
      });

      // Add realistic water layer
      _mapController!
          .addFillLayer(
        'openmaptiles',
        'custom-water',
        const FillLayerProperties(
          fillColor: '#7C9CAE', // Realistic blue-gray water
          fillOpacity: 0.95,
        ),
        sourceLayer: 'water',
        minzoom: 0.0,
      )
          .then((_) {
        debugPrint('Successfully added custom water layer');
      }).catchError((e) {
        debugPrint('Error adding water layer: $e');
      });

      // Add subtle water outline
      _mapController!
          .addLineLayer(
        'openmaptiles',
        'water-outline',
        const LineLayerProperties(
          lineColor: '#6B8899',
          lineWidth: [
            'interpolate',
            ['linear'],
            ['zoom'],
            10,
            0.5,
            14,
            1,
            18,
            1.5,
          ],
          lineOpacity: 0.5,
        ),
        sourceLayer: 'water',
        minzoom: 10.0,
      )
          .then((_) {
        debugPrint('Successfully added water outline');
      }).catchError((e) {
        debugPrint('Error adding water outline: $e');
      });
    } catch (e) {
      debugPrint('Error enhancing water features: $e');
    }
  }

  void _customizeRoads() {
    if (_mapController == null) return;

    try {
      // Create realistic road surfaces

      // Add road surface with realistic asphalt color
      _mapController!
          .addLineLayer(
        'openmaptiles',
        'road-surface',
        const LineLayerProperties(
          lineColor: [
            'case',
            [
              '==',
              ['get', 'class'],
              'motorway'
            ],
            '#3A3A3A',
            [
              '==',
              ['get', 'class'],
              'trunk'
            ],
            '#404040',
            [
              '==',
              ['get', 'class'],
              'primary'
            ],
            '#484848',
            [
              '==',
              ['get', 'class'],
              'secondary'
            ],
            '#505050',
            [
              '==',
              ['get', 'class'],
              'tertiary'
            ],
            '#585858',
            '#606060' // Default for minor roads
          ],
          lineWidth: [
            'interpolate',
            ['linear'],
            ['zoom'],
            10, 1, // Reduced from 2
            14, 3, // Reduced from 6
            18, 7, // Reduced from 14
          ],
        ),
        sourceLayer: 'transportation',
        minzoom: 10.0,
        belowLayerId: 'building-3d',
      )
          .catchError((e) {
        debugPrint('Error adding road surface: $e');
      });

      // Add road edge lines for definition
      _mapController!
          .addLineLayer(
        'openmaptiles',
        'road-edge',
        const LineLayerProperties(
          lineColor: '#2A2A2A',
          lineWidth: [
            'interpolate',
            ['linear'],
            ['zoom'],
            14, 0.3, // Reduced from 0.5
            18, 0.8, // Reduced from 1.5
          ],
        ),
        sourceLayer: 'transportation',
        filter: [
          'in',
          ['get', 'class'],
          [
            'literal',
            ['motorway', 'trunk', 'primary']
          ]
        ],
        minzoom: 14.0,
        belowLayerId: 'building-3d',
      )
          .catchError((e) {
        debugPrint('Error adding road edges: $e');
      });

      // Add subtle road markings
      _mapController!
          .addLineLayer(
        'openmaptiles',
        'road-markings',
        const LineLayerProperties(
          lineColor: '#E8E8E8',
          lineWidth: 0.5, // Reduced from 1
          lineDasharray: [10, 10],
          lineOpacity: 0.6,
        ),
        sourceLayer: 'transportation',
        filter: [
          'in',
          ['get', 'class'],
          [
            'literal',
            ['motorway', 'trunk', 'primary']
          ]
        ],
        minzoom: 16.0,
        belowLayerId: 'building-3d',
      )
          .catchError((e) {
        debugPrint('Error adding road markings: $e');
      });

      debugPrint('🛣️ Added realistic road surfaces');
    } catch (e) {
      debugPrint('Error customizing roads: $e');
    }
  }

  // Add all text and label layers on top to ensure they're always visible
  void _addAllLabelsOnTop() {
    if (_mapController == null) return;

    debugPrint('🏷️ Adding all labels on top...');

    // Add building labels and icons if 3D buildings are enabled
    if (widget.show3DBuildings == true) {
      _addBuildingLabels();
    }

    // Add road labels
    _addRoadLabels();

    // Add place labels
    _addPlaceLabels();

    // Add water feature labels
    _addWaterLabels();

    // Add POI (Point of Interest) labels
    _addPOILabels();

    debugPrint('🏷️ All labels added on top');
  }

  // Add road labels with better styling
  void _addRoadLabels() {
    if (_mapController == null) return;

    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    final isDarkMode = themeProvider.isDarkMode;

    _mapController!.addSymbolLayer(
      'openmaptiles',
      'road-labels',
      SymbolLayerProperties(
        textField: ['get', 'name'],
        textFont: [
          'literal',
          ['Open Sans Medium', 'Arial Unicode MS Regular']
        ],
        textSize: [
          'interpolate',
          ['linear'],
          ['zoom'],
          12,
          10,
          16,
          12,
          18,
          14,
        ],
        textColor: isDarkMode ? '#FFFFFF' : '#2C3E50',
        textHaloColor: isDarkMode ? '#000000' : '#FFFFFF',
        textHaloWidth: 1.5,
        textHaloBlur: 0.5,
        symbolPlacement: 'line',
        textRotationAlignment: 'map',
        textPitchAlignment: 'viewport',
        textMaxAngle: 30,
        textKeepUpright: true,
        textAllowOverlap: false,
        textIgnorePlacement: false,
        symbolSpacing: 200,
      ),
      sourceLayer: 'transportation_name',
      minzoom: 12.0,
      filter: ['has', 'name'],
    ).catchError((e) {
      debugPrint('Could not add road labels: $e');
    });
  }

  // Add place labels (cities, neighborhoods, etc.)
  void _addPlaceLabels() {
    if (_mapController == null) return;

    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    final isDarkMode = themeProvider.isDarkMode;

    _mapController!.addSymbolLayer(
      'openmaptiles',
      'place-labels',
      SymbolLayerProperties(
        textField: ['get', 'name'],
        textFont: [
          'literal',
          ['Open Sans Bold', 'Arial Unicode MS Bold']
        ],
        textSize: [
          'interpolate',
          ['linear'],
          ['get', 'rank'],
          1, 16, // Large cities
          5, 14, // Medium cities
          10, 12, // Small cities
          15, 10, // Neighborhoods
        ],
        textColor: isDarkMode ? '#FFFFFF' : '#2C3E50',
        textHaloColor: isDarkMode ? '#000000' : '#FFFFFF',
        textHaloWidth: 2.0,
        textHaloBlur: 0.5,
        textAnchor: 'center',
        textJustify: 'center',
        textAllowOverlap: false,
        textIgnorePlacement: false,
        symbolSpacing: 300,
        textOpacity: [
          'interpolate',
          ['linear'],
          ['zoom'],
          8,
          0.8,
          12,
          1.0,
        ],
      ),
      sourceLayer: 'place',
      minzoom: 6.0,
      filter: ['has', 'name'],
    ).catchError((e) {
      debugPrint('Could not add place labels: $e');
    });
  }

  // Add water feature labels
  void _addWaterLabels() {
    if (_mapController == null) return;

    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    final isDarkMode = themeProvider.isDarkMode;

    _mapController!.addSymbolLayer(
      'openmaptiles',
      'water-labels',
      SymbolLayerProperties(
        textField: ['get', 'name'],
        textFont: [
          'literal',
          ['Open Sans Italic', 'Arial Unicode MS Regular']
        ],
        textSize: [
          'interpolate',
          ['linear'],
          ['zoom'],
          10,
          10,
          14,
          12,
          18,
          14,
        ],
        textColor: isDarkMode ? '#87CEEB' : '#4682B4',
        textHaloColor: isDarkMode ? '#000000' : '#FFFFFF',
        textHaloWidth: 1.5,
        textHaloBlur: 0.5,
        textAnchor: 'center',
        textJustify: 'center',
        textAllowOverlap: false,
        textIgnorePlacement: false,
        symbolSpacing: 250,
      ),
      sourceLayer: 'water_name',
      minzoom: 10.0,
      filter: ['has', 'name'],
    ).catchError((e) {
      debugPrint('Could not add water labels: $e');
    });
  }

  // Add Point of Interest labels
  void _addPOILabels() {
    if (_mapController == null) return;

    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    final isDarkMode = themeProvider.isDarkMode;

    // First add POI icons
    _addPOIIcons();

    // Wait a bit for icons to be added, then add the symbol layer
    Future.delayed(const Duration(milliseconds: 500), () {
      if (_mapController == null) return;

      _mapController!.addSymbolLayer(
        'openmaptiles',
        'poi-labels',
        SymbolLayerProperties(
          // Icon configuration
          iconImage: [
            'case',
            [
              '==',
              ['get', 'class'],
              'restaurant'
            ],
            'poi-restaurant',
            [
              '==',
              ['get', 'class'],
              'cafe'
            ],
            'poi-cafe',
            [
              '==',
              ['get', 'class'],
              'hotel'
            ],
            'poi-hotel',
            [
              '==',
              ['get', 'class'],
              'hospital'
            ],
            'poi-hospital',
            [
              '==',
              ['get', 'class'],
              'school'
            ],
            'poi-school',
            [
              '==',
              ['get', 'class'],
              'bank'
            ],
            'poi-bank',
            [
              '==',
              ['get', 'class'],
              'shop'
            ],
            'poi-shop',
            [
              '==',
              ['get', 'class'],
              'museum'
            ],
            'poi-museum',
            [
              '==',
              ['get', 'class'],
              'library'
            ],
            'poi-library',
            [
              '==',
              ['get', 'class'],
              'park'
            ],
            'poi-park',
            'poi-default' // Default POI icon
          ],
          iconSize: [
            'interpolate',
            ['linear'],
            ['zoom'],
            14,
            0.6,
            16,
            0.8,
            18,
            1.0,
          ],
          iconAllowOverlap: false,
          iconIgnorePlacement: false,
          iconAnchor: 'bottom', // Anchor icon at bottom so text appears above

          // Text configuration
          textField: ['get', 'name'],
          textFont: [
            'literal',
            ['Open Sans Regular', 'Arial Unicode MS Regular']
          ],
          textSize: [
            'interpolate',
            ['linear'],
            ['zoom'],
            14,
            9,
            16,
            11,
            18,
            13,
          ],
          textColor: isDarkMode ? '#FFD700' : '#8B4513',
          textHaloColor: isDarkMode ? '#000000' : '#FFFFFF',
          textHaloWidth: 1.5,
          textHaloBlur: 0.5,
          textAnchor: 'bottom', // Anchor text at bottom
          textOffset: [0, -1.8], // Offset text above the icon
          textJustify: 'center',
          textAllowOverlap: false,
          textIgnorePlacement: false,
          symbolSpacing: 200,
          textMaxWidth: 8,
          symbolPlacement: 'point',
        ),
        sourceLayer: 'poi',
        minzoom: 14.0,
        filter: [
          'all',
          ['has', 'name'],
          [
            'in',
            ['get', 'class'],
            [
              'literal',
              [
                'restaurant',
                'cafe',
                'hotel',
                'hospital',
                'school',
                'bank',
                'shop',
                'museum',
                'library',
                'park'
              ]
            ]
          ]
        ],
      ).then((_) {
        debugPrint('🎯 Added POI labels with icons');
      }).catchError((e) {
        debugPrint('Could not add POI labels: $e');
      });
    });
  }

  // Apply retro/80s effects
  void _applyRetroEffects() {
    if (_mapController == null) return;

    debugPrint('🎮 Applying retro effects...');
    debugPrint(
        '📺 Current B&W state: ${_visualEffectsManager.isBlackAndWhiteActive}');

    try {
      // Clean up ALL existing retro layers first to prevent conflicts
      final retroLayers = [
        'retro-roads',
        'retro-buildings',
        'retro-water',
        'retro-parks',
        'retro-grass',
        'retro-forest',
        'retro-roads-bw',
        'retro-buildings-bw',
        'retro-water-bw',
        'retro-parks-bw',
        'retro-grass-bw',
        'retro-forest-bw',
        'retro-landcover-bw',
        'retro-place-labels-bw',
        'retro-road-labels-bw',
      ];

      for (final layerId in retroLayers) {
        _mapController!.removeLayer(layerId).catchError((e) {
          // Layer might not exist, that's okay
        });
      }

      // Remove existing building layers to prevent z-fighting
      _removeAllBuildingLayers();

      debugPrint(
          '📺 Applying ${_visualEffectsManager.isBlackAndWhiteActive ? 'B&W' : 'color'} retro layers...');

      if (_visualEffectsManager.isBlackAndWhiteActive) {
        // Apply black and white retro effects
        _applyBlackAndWhiteRetroEffects();
      } else {
        // Apply colored retro effects
        _applyColoredRetroEffects();
      }
    } catch (e) {
      debugPrint('Error applying retro effects: $e');
    }
  }

  // Apply colored retro effects (normal retro mode)
  void _applyColoredRetroEffects() {
    debugPrint('🌈 Applying colored retro effects...');

    // Vibrant retro roads with classic 80s colors
    _mapController!
        .addLineLayer(
          'openmaptiles',
          'retro-roads',
          const LineLayerProperties(
            lineColor: [
              'case',
              [
                '==',
                ['get', 'class'],
                'motorway'
              ],
              '#FF6B9D', // Hot pink
              [
                '==',
                ['get', 'class'],
                'trunk'
              ],
              '#FF6B9D', // Hot pink
              [
                '==',
                ['get', 'class'],
                'primary'
              ],
              '#4ECDC4', // Teal
              [
                '==',
                ['get', 'class'],
                'secondary'
              ],
              '#45B7D1', // Sky blue
              '#96CEB4' // Mint green
            ],
            lineWidth: [
              'interpolate',
              ['linear'],
              ['zoom'],
              10,
              1,
              14,
              2.5,
              18,
              5,
            ],
            lineOpacity: 0.9,
          ),
          sourceLayer: 'transportation',
          minzoom: 10.0,
          belowLayerId: 'poi-labels',
        )
        .catchError((e) => debugPrint('Error adding colored retro roads: $e'));

    // Add colored retro buildings
    if (widget.show3DBuildings == true) {
      _mapController!
          .addFillExtrusionLayer(
            'openmaptiles',
            'retro-buildings',
            const FillExtrusionLayerProperties(
              fillExtrusionColor: [
                'interpolate',
                ['linear'],
                ['get', 'render_height'],
                0, '#FF6B9D', // Hot pink
                20, '#4ECDC4', // Teal
                40, '#45B7D1', // Sky blue
                60, '#96CEB4', // Mint green
                80, '#FECA57', // Golden yellow
                100, '#FF9FF3', // Light pink
              ],
              fillExtrusionHeight: [
                'interpolate',
                ['linear'],
                ['zoom'],
                15,
                [
                  '*',
                  ['get', 'render_height'],
                  1.2
                ],
                18,
                [
                  '*',
                  ['get', 'render_height'],
                  2.5
                ],
              ],
              fillExtrusionBase: [
                'interpolate',
                ['linear'],
                ['zoom'],
                15,
                0,
                16,
                ['get', 'render_min_height']
              ],
              fillExtrusionOpacity: 0.8,
              fillExtrusionVerticalGradient: true,
            ),
            sourceLayer: 'building',
            minzoom: 14.0,
            filter: [
              'all',
              [
                '>',
                ['get', 'render_height'],
                0
              ],
              [
                '!=',
                ['get', 'type'],
                'underground'
              ],
            ],
            belowLayerId: 'poi-labels',
          )
          .catchError(
              (e) => debugPrint('Error adding colored retro buildings: $e'));
    }

    // Add colored water
    _mapController!
        .addFillLayer(
          'openmaptiles',
          'retro-water',
          const FillLayerProperties(
            fillColor: '#4ECDC4', // Retro teal
            fillOpacity: 0.8,
          ),
          sourceLayer: 'water',
          minzoom: 0.0,
          belowLayerId: 'poi-labels',
        )
        .catchError((e) => debugPrint('Error adding colored retro water: $e'));

    // Add colored parks
    _mapController!
        .addFillLayer(
          'openmaptiles',
          'retro-parks',
          const FillLayerProperties(
            fillColor: '#96CEB4', // Retro mint green
            fillOpacity: 0.7,
          ),
          sourceLayer: 'park',
          minzoom: 10.0,
          belowLayerId: 'poi-labels',
        )
        .catchError((e) => debugPrint('Error adding colored retro parks: $e'));

    // Add colored grass/meadow areas
    _mapController!
        .addFillLayer(
          'openmaptiles',
          'retro-grass',
          const FillLayerProperties(
            fillColor: '#A8E6CF', // Light mint green for grass
            fillOpacity: 0.6,
          ),
          sourceLayer: 'landcover',
          filter: [
            'in',
            ['get', 'class'],
            [
              'literal',
              ['grass', 'meadow', 'scrub']
            ]
          ],
          minzoom: 11.0,
          belowLayerId: 'poi-labels',
        )
        .catchError((e) => debugPrint('Error adding colored retro grass: $e'));

    // Add colored forest/wood areas
    _mapController!
        .addFillLayer(
          'openmaptiles',
          'retro-forest',
          const FillLayerProperties(
            fillColor: '#7FCDCD', // Retro teal-green for forests
            fillOpacity: 0.8,
          ),
          sourceLayer: 'landcover',
          filter: [
            'in',
            ['get', 'class'],
            [
              'literal',
              ['wood', 'forest']
            ]
          ],
          minzoom: 10.0,
          belowLayerId: 'poi-labels',
        )
        .catchError((e) => debugPrint('Error adding colored retro forest: $e'));
  }

  // Apply only style-specific effects without touching pin layers
  Future<void> _applyStyleEffectsOnly() async {
    debugPrint('🎨 Applying style effects only (preserving pin layers)');

    try {
      // Apply style-specific effects based on current map style
      switch (_currentMapStyle) {
        case MapStyle.neon:
          _mapStyleManager.applyNeonEffects();
          break;
        case MapStyle.cyberpunk:
          _mapStyleManager.applyCyberpunkEffects();
          break;
        case MapStyle.retro:
          _applyRetroEffects();
          break;
        case MapStyle.matrix:
          _mapStyleManager.applyMatrixEffects();
          break;
        case MapStyle.vaporwave:
          _mapStyleManager.applyVaporwaveEffects();
          break;
        case MapStyle.minimal:
          _mapStyleManager.applyMinimalEffects();
          break;
        case MapStyle.oldSchool:
          _mapStyleManager.applyOldSchoolEffects();
          break;
        case MapStyle.watercolor:
          _mapStyleManager.applyWatercolorEffects();
          break;
        case MapStyle.futuristic:
          _mapStyleManager.applyFuturisticEffects();
          break;
        default:
          // For standard style, apply buildings if 3D is enabled
          if (widget.show3DBuildings == true) {
            _mapStyleManager.add3DBuildingLayer();
          }
          break;
      }

      // Add all text and label layers LAST to ensure they appear on top
      Future.delayed(const Duration(milliseconds: 300), () {
        _addAllLabelsOnTop();
      });

      debugPrint('🎨 ✅ Style effects applied successfully without touching pin layers');
    } catch (e) {
      debugPrint('🎨 ❌ Error applying style effects only: $e');
    }
  }

  // Apply black and white retro effects (TV mode)
  void _applyBlackAndWhiteRetroEffects() {
    debugPrint('📺 Applying black and white retro effects...');

    // Black and white roads
    _mapController!
        .addLineLayer(
          'openmaptiles',
          'retro-roads-bw',
          const LineLayerProperties(
            lineColor: [
              'case',
              [
                '==',
                ['get', 'class'],
                'motorway'
              ],
              '#FFFFFF', // White for major roads
              [
                '==',
                ['get', 'class'],
                'trunk'
              ],
              '#FFFFFF', // White
              [
                '==',
                ['get', 'class'],
                'primary'
              ],
              '#CCCCCC', // Light gray
              [
                '==',
                ['get', 'class'],
                'secondary'
              ],
              '#999999', // Medium gray
              '#666666' // Dark gray for minor roads
            ],
            lineWidth: [
              'interpolate',
              ['linear'],
              ['zoom'],
              10,
              1,
              14,
              2.5,
              18,
              5,
            ],
            lineOpacity: 0.9,
          ),
          sourceLayer: 'transportation',
          minzoom: 10.0,
          belowLayerId: 'poi-labels',
        )
        .catchError((e) => debugPrint('Error adding B&W retro roads: $e'));

    // Black and white buildings
    if (widget.show3DBuildings == true) {
      _mapController!
          .addFillExtrusionLayer(
            'openmaptiles',
            'retro-buildings-bw',
            const FillExtrusionLayerProperties(
              fillExtrusionColor: [
                'interpolate',
                ['linear'],
                ['get', 'render_height'],
                0, '#FFFFFF', // White for short buildings
                20, '#EEEEEE', // Very light gray
                40, '#CCCCCC', // Light gray
                60, '#AAAAAA', // Medium gray
                80, '#888888', // Dark gray
                100, '#666666', // Very dark gray for tall buildings
              ],
              fillExtrusionHeight: [
                'interpolate',
                ['linear'],
                ['zoom'],
                15,
                [
                  '*',
                  ['get', 'render_height'],
                  1.2
                ],
                18,
                [
                  '*',
                  ['get', 'render_height'],
                  2.5
                ],
              ],
              fillExtrusionBase: [
                'interpolate',
                ['linear'],
                ['zoom'],
                15,
                0,
                16,
                ['get', 'render_min_height']
              ],
              fillExtrusionOpacity: 0.8,
              fillExtrusionVerticalGradient: true,
            ),
            sourceLayer: 'building',
            minzoom: 14.0,
            filter: [
              'all',
              [
                '>',
                ['get', 'render_height'],
                0
              ],
              [
                '!=',
                ['get', 'type'],
                'underground'
              ],
            ],
            belowLayerId: 'poi-labels',
          )
          .catchError(
              (e) => debugPrint('Error adding B&W retro buildings: $e'));
    }

    // Black and white water
    _mapController!
        .addFillLayer(
          'openmaptiles',
          'retro-water-bw',
          const FillLayerProperties(
            fillColor: '#777777', // Medium dark gray water
            fillOpacity: 0.9,
          ),
          sourceLayer: 'water',
          minzoom: 0.0,
          belowLayerId: 'poi-labels',
        )
        .catchError((e) => debugPrint('Error adding B&W retro water: $e'));

    // Black and white parks
    _mapController!
        .addFillLayer(
          'openmaptiles',
          'retro-parks-bw',
          const FillLayerProperties(
            fillColor: '#CCCCCC', // Light gray parks
            fillOpacity: 0.8,
          ),
          sourceLayer: 'park',
          minzoom: 10.0,
          belowLayerId: 'poi-labels',
        )
        .catchError((e) => debugPrint('Error adding B&W retro parks: $e'));

    // Black and white grass/meadow areas
    _mapController!
        .addFillLayer(
          'openmaptiles',
          'retro-grass-bw',
          const FillLayerProperties(
            fillColor: '#DDDDDD', // Very light gray for grass
            fillOpacity: 0.7,
          ),
          sourceLayer: 'landcover',
          filter: [
            'in',
            ['get', 'class'],
            [
              'literal',
              ['grass', 'meadow', 'scrub']
            ]
          ],
          minzoom: 11.0,
          belowLayerId: 'poi-labels',
        )
        .catchError((e) => debugPrint('Error adding B&W retro grass: $e'));

    // Black and white forest/wood areas
    _mapController!
        .addFillLayer(
          'openmaptiles',
          'retro-forest-bw',
          const FillLayerProperties(
            fillColor: '#AAAAAA', // Medium gray for forests
            fillOpacity: 0.8,
          ),
          sourceLayer: 'landcover',
          filter: [
            'in',
            ['get', 'class'],
            [
              'literal',
              ['wood', 'forest']
            ]
          ],
          minzoom: 10.0,
          belowLayerId: 'poi-labels',
        )
        .catchError((e) => debugPrint('Error adding B&W retro forest: $e'));

    // Black and white general landcover (catch-all for other land types)
    _mapController!
        .addFillLayer(
          'openmaptiles',
          'retro-landcover-bw',
          const FillLayerProperties(
            fillColor: '#E5E5E5', // Very light gray for other landcover
            fillOpacity: 0.5,
          ),
          sourceLayer: 'landcover',
          filter: [
            'all',
            [
              '!=',
              ['get', 'class'],
              'grass'
            ],
            [
              '!=',
              ['get', 'class'],
              'meadow'
            ],
            [
              '!=',
              ['get', 'class'],
              'scrub'
            ],
            [
              '!=',
              ['get', 'class'],
              'wood'
            ],
            [
              '!=',
              ['get', 'class'],
              'forest'
            ]
          ],
          minzoom: 10.0,
          belowLayerId: 'poi-labels',
        )
        .catchError(
            (e) => debugPrint('Error adding B&W retro general landcover: $e'));

    // Black and white labels
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    _addBlackAndWhiteLabels();
  }

  // Add black and white labels for TV effect
  void _addBlackAndWhiteLabels() {
    // Place labels in black and white
    _mapController!.addSymbolLayer(
      'openmaptiles',
      'retro-place-labels-bw',
      const SymbolLayerProperties(
        textField: ['get', 'name'],
        textFont: [
          'literal',
          ['Open Sans Bold', 'Arial Unicode MS Bold']
        ],
        textSize: [
          'interpolate',
          ['linear'],
          ['get', 'rank'],
          1,
          16,
          5,
          14,
          10,
          12,
          15,
          10,
        ],
        textColor: '#000000', // Pure black text
        textHaloColor: '#FFFFFF', // White halo
        textHaloWidth: 2.0,
        textHaloBlur: 0.5,
        textAnchor: 'center',
        textJustify: 'center',
        textAllowOverlap: false,
        textIgnorePlacement: false,
        symbolSpacing: 300,
        textOpacity: 1.0,
      ),
      sourceLayer: 'place',
      minzoom: 6.0,
      filter: ['has', 'name'],
    ).catchError((e) => debugPrint('Could not add B&W place labels: $e'));

    // Road labels in black and white
    _mapController!.addSymbolLayer(
      'openmaptiles',
      'retro-road-labels-bw',
      const SymbolLayerProperties(
        textField: ['get', 'name'],
        textFont: [
          'literal',
          ['Open Sans Medium', 'Arial Unicode MS Regular']
        ],
        textSize: [
          'interpolate',
          ['linear'],
          ['zoom'],
          12,
          10,
          16,
          12,
          18,
          14,
        ],
        textColor: '#000000', // Pure black text
        textHaloColor: '#FFFFFF', // White halo
        textHaloWidth: 1.5,
        textHaloBlur: 0.5,
        symbolPlacement: 'line',
        textRotationAlignment: 'map',
        textPitchAlignment: 'viewport',
        textMaxAngle: 30,
        textKeepUpright: true,
        textAllowOverlap: false,
        textIgnorePlacement: false,
        symbolSpacing: 200,
      ),
      sourceLayer: 'transportation_name',
      minzoom: 12.0,
      filter: ['has', 'name'],
    ).catchError((e) => debugPrint('Could not add B&W road labels: $e'));
  }

  void _toggleVegetation() {
    widget.onToggleVegetation?.call();

    // Handle the actual vegetation toggle based on current state
    if (_mapController != null) {
      if (widget.showVegetation == true) {
        // Re-add vegetation layers
        _addParksAndVegetation();
        _addTreeSymbols();
        _enhanceWaterFeatures();
      } else {
        // Remove vegetation layers
        final layersToRemove = [
          'custom-parks',
          'custom-grass',
          'custom-forest',
          'park-overlay',
          'park-trees',
          'landcover-trees',
          'custom-water',
          'water-outline',
        ];

        for (final layer in layersToRemove) {
          _mapController!.removeLayer(layer).catchError((e) {
            debugPrint('Could not remove layer $layer: $e');
          });
        }
      }
    }
  }

  void _handle3DBuildingsToggle() {
    if (_mapController != null) {
      if (widget.show3DBuildings == true) {
        // Always remove existing building layers first to prevent z-fighting
        _mapStyleManager.removeAllBuildingLayers();

        // Apply style-specific building effects
        switch (_currentMapStyle) {
          case MapStyle.neon:
            _mapStyleManager.applyNeonEffects();
            break;
          case MapStyle.cyberpunk:
            _mapStyleManager.applyCyberpunkEffects();
            break;
          case MapStyle.retro:
            _mapStyleManager.applyRetroEffects();
            break;
          case MapStyle.matrix:
            _mapStyleManager.applyMatrixEffects();
            break;
          case MapStyle.vaporwave:
            _mapStyleManager.applyVaporwaveEffects();
            break;
          case MapStyle.minimal:
            _mapStyleManager.applyMinimalEffects();
            break;
          case MapStyle.oldSchool:
            _mapStyleManager.applyOldSchoolEffects();
            break;
          case MapStyle.watercolor:
            _mapStyleManager.applyWatercolorEffects();
            break;
          case MapStyle.futuristic:
            _mapStyleManager.applyFuturisticEffects();
            break;
          default:
            _mapStyleManager.add3DBuildingLayer();
            break;
        }

        // Ensure user avatar and scanning stay on top after buildings are added
        Future.delayed(const Duration(milliseconds: 200), () {
          if (mounted && _isMapReady) {
            _ensureUserAvatarOnTop();

            // Restart scanning if active to ensure it's on top
            if (_isScanningActive) {
              _stopScanningAnimation();
              _startScanningAnimation();
            }
          }
        });

        // Automatically tilt to reveal 3D buildings when they're enabled
        Future.delayed(const Duration(milliseconds: 300), () {
          _autoTiltForBuildings();
        });
      } else {
        // Remove all building-related layers
        final buildingLayers = [
          'building-3d',
          'building-3d-residential',
          'building-3d-commercial',
          'building-3d-industrial',
          'building-3d-alt',
          'building-labels',
          'building-shadows',
          'building-ground-shadows',
          'building-ambient-occlusion',
          'building-shadows-tall',
          'building-shadows-medium',
          'building-shadows-short',
          'building-contact-shadows',
          'building-shadow-blur-0',
          'building-shadow-blur-1',
          'building-shadow-blur-2',
          'building-shadows-alt',
          'building-atmosphere',
          'building-atmosphere-simple',
          'neon-building-3d',
          'building-neon-glow',
          'building-neon-edges',
          'building-base-glow',
          'building-ground-glow',
          'cyberpunk-buildings',
          'retro-buildings',
          'matrix-buildings',
          'vaporwave-buildings',
          'minimal-buildings',
          'oldschool-buildings',
          'oldschool-buildings-2d',
          'watercolor-buildings',
          'futuristic-buildings',
          'futuristic-building-edges',
          'futuristic-building-glow',
        ];

        for (final layer in buildingLayers) {
          _mapController!.removeLayer(layer).catchError((e) {
            debugPrint('Could not remove layer $layer: $e');
          });
        }

        // Reset tilt for 2D view
        _mapController!.animateCamera(
          CameraUpdate.tiltTo(0.0),
          duration: const Duration(milliseconds: 500),
        );

        // Ensure user avatar remains visible after buildings are removed
        Future.delayed(const Duration(milliseconds: 200), () {
          if (mounted && _isMapReady) {
            _ensureUserAvatarOnTop();

            // Also ensure scanning animation is on top if active
            if (_isScanningActive) {
              _stopScanningAnimation();
              _startScanningAnimation();
            }
          }
        });
      }
    }
  }

  // Update pins using cached data only (no API calls)
  Future<void> _updateMapPins() async {
    if (_mapController == null || _currentPosition == null) return;

    debugPrint(
        '📍 _updateMapPins called - updating from cache only (no API calls)');

    // Only update from cache - API calls are handled by location updates
    if (_pinCache.isNotEmpty) {
      await _updateMapPinsFromCache();
    } else {
      debugPrint('📍 No cached pins available for display');
    }
  }

  // Legacy method - now only handles rendering, not API fetching
  Future<void> _performMapPinsUpdate() async {
    debugPrint('📍 _performMapPinsUpdate called - now only handles rendering');

    if (_mapController == null || _currentPosition == null) return;

    // Double-check if fetch is still needed after debounce delay
    if (_isPinFetchInProgress) {
      debugPrint('📍 Pin fetch already in progress after debounce, skipping');
      return;
    }

    debugPrint('📍 Starting optimized pin rendering...');
    debugPrint(
        '📍 Current zoom: $_currentZoom, will cluster: ${_currentZoom < _maxZoomForClustering}');
    _pinUpdateCount++;
    _lastPinUpdate = DateTime.now();

    // Use progressive rendering if enabled and we have many pins to load
    final cacheCount = _pinCache.length;
    final providerCount = _mapProvider?.pins.length ?? 0;
    final allPinsCount = _allPinsData.length;
    final estimatedPinCount =
        math.max(cacheCount, math.max(providerCount, allPinsCount));

    debugPrint(
        '📍 Progressive rendering check: enabled=$_isProgressiveRenderingEnabled');
    debugPrint(
        '📍 Pin counts - cache:$cacheCount, provider:$providerCount, allPins:$allPinsCount, estimated:$estimatedPinCount');

    if (shouldUseProgressiveRendering(estimatedPinCount)) {
      debugPrint(
          '📍 ✅ Using progressive rendering for $estimatedPinCount pins');
      await _updateMapPinsProgressively();
      return;
    } else {
      debugPrint(
          '📍 ❌ Using batch rendering for $estimatedPinCount pins (progressive disabled or below threshold)');
    }

    try {
      _isPinFetchInProgress = true;

      // Start scanning animation for visual feedback
      _startScanningAnimation();

      // Determine if we should show clusters or individual pins
      final shouldCluster = _currentZoom < _maxZoomForClustering;
      final cacheKey = 'zoom_${_currentZoom.toStringAsFixed(1)}';

      // Check if we need to rebuild layers (only when data changes, not zoom)
      // Force rebuild if we have new pins (detected by checking cache sync)
      final hasNewPins =
          _pinCache.values.any((pin) => pin['synced_from_provider'] == true);
      final needsLayerRebuild = !_layersInitialized ||
          (_testPinSymbols.isEmpty &&
              _clusterSymbols.isEmpty &&
              _pinCache.isNotEmpty) ||
          hasNewPins;

      // For zoom-only changes, just switch layer visibility instantly
      if (_layersInitialized && !needsLayerRebuild) {
        final needsVisibilitySwitch =
            _currentlyShowingClusterLayer != shouldCluster;
        if (needsVisibilitySwitch) {
          await _switchLayerVisibility(shouldCluster);
          _currentlyShowingClusterLayer = shouldCluster;
          _isCurrentlyShowingClusters = shouldCluster;
          debugPrint(
              '📍 ⚡ Instant layer switch: ${shouldCluster ? 'clusters' : 'pins'} (zoom: $_currentZoom)');
          return;
        } else {
          debugPrint('📍 No layer change needed (zoom: $_currentZoom)');
          return;
        }
      }

      debugPrint(
          '📍 ${_layersInitialized ? 'Rebuilding' : 'Initializing'} pin layers: zoom=$_currentZoom');

      // Clear existing symbols efficiently (only if rebuilding)
      // if (_layersInitialized) {
      //   await _clearExistingSymbols();
      // }

      // Clear synced_from_provider flag to prevent continuous rebuilds
      if (hasNewPins) {
        for (final pin in _pinCache.values) {
          if (pin['synced_from_provider'] == true) {
            pin.remove('synced_from_provider');
          }
        }
      }

      // Use cached pin data only (no API fetching)
      final pinData = _pinCache.values.toList();

      if (pinData.isEmpty) {
        debugPrint('📍 No pin data available, skipping display');
        return;
      }

      // Add pin icons if not already added
      await _addPinIcons();

      // Remove existing glow layers before adding new ones
      await _removeGlowLayers();

      // Use progressive rendering instead of legacy batch rendering
      try {
        debugPrint(
            '📍 🎬 Using progressive rendering for ${pinData.length} pins in _performMapPinsUpdate');

        // Clear pending pins and prepare for progressive rendering
        _pendingPins.clear();

        // Only clear rendered pins for specific contexts that require fresh rendering
        if (_currentRenderingContext == RenderingContext.initialLoad ||
            _currentRenderingContext == RenderingContext.mapStyleChange ||
            _currentRenderingContext == RenderingContext.filterChange) {
          _renderedPinIds.clear();
          debugPrint('📍 🔄 Cleared rendered pins for fresh ${_currentRenderingContext.name} rendering in progressive mode');
        }

        // Sort pins by distance from user location (nearest first) for progressive rendering
        final sortedPins = _sortPinsByDistanceFromUser(pinData);

        // For accumulative rendering, only add unrendered pins
        if (_currentRenderingContext != RenderingContext.initialLoad &&
            _currentRenderingContext != RenderingContext.mapStyleChange &&
            _currentRenderingContext != RenderingContext.filterChange) {
          // Filter out already rendered pins to maintain accumulative behavior
          final unrenderedPins = sortedPins.where((pin) {
            final pinId = pin['id']?.toString();
            return pinId != null && !_renderedPinIds.contains(pinId);
          }).toList();
          _pendingPins.addAll(unrenderedPins);
          debugPrint('📍 🔄 Added ${unrenderedPins.length} unrendered pins for accumulative progressive rendering');
        } else {
          _pendingPins.addAll(sortedPins);
          debugPrint('📍 🔄 Added ${sortedPins.length} pins for fresh progressive rendering');
        }

        // STRATEGIC PRE-CLUSTERING: Always calculate clustering strategy for both layers
        await _preCalculateClusteringStrategy(_pendingPins);

        // Start progressive rendering
        _startProgressiveRendering();

        debugPrint(
            '📍 ✅ Started progressive rendering for ${pinData.length} pins');
      } catch (e) {
        debugPrint('📍 Error starting progressive rendering: $e');
        _stopScanningAnimation();
      }

      _lastClusteringZoom = _currentZoom;

      // Glow layers are already handled by the layer-based approach; no extra glow addition here

      // Configure symbol settings
      await _configureSymbolSettings();

      // Start animations
      _startOptimizedAnimations();

      // Mark initial load as complete after first successful pin load
      if (_isInitialPinLoad) {
        _isInitialPinLoad = false;
        debugPrint('📍 Initial pin load completed - animations now enabled');
      }

      // Update pins in aura
      _updatePinsInAura();

      final updateTime =
          DateTime.now().difference(_lastPinUpdate!).inMilliseconds;
      debugPrint(
          '📍 Optimized pin update #$_pinUpdateCount completed in ${updateTime}ms');
      debugPrint(
          '📍 Cache stats - Pins: ${_pinCache.length}, Clusters: ${_clusterCache.length}, Icons: ${_createdClusterIcons.length}');

      // Validate final pin state
      _validatePinState();
    } catch (e) {
      debugPrint('📍 Error in optimized pin update: $e');
      // Still validate state even after errors
      _validatePinState();
    } finally {
      _isPinFetchInProgress = false;

      // Stop scanning animation when pin fetching completes
      // _stopScanningAnimation();
    }
  }

  // Progressive pin rendering implementation
  Future<void> _updateMapPinsProgressively() async {
    debugPrint('📍 🚀 Starting progressive pin rendering (context: $_currentRenderingContext)...');

    // CRITICAL: Atomic check and set to prevent multiple concurrent renders
    if (_isProgressiveRenderingActive) {
      debugPrint('📍 ⚠️ Progressive rendering already active, skipping');
      return;
    }

    // Check if progressive rendering timer is already active
    if (_progressiveUpdateTimer?.isActive == true) {
      debugPrint('📍 ⚠️ Progressive rendering timer already active, skipping');
      return;
    }

    // Set atomic flag immediately to prevent race conditions
    _isProgressiveRenderingActive = true;
    debugPrint('📍 🔒 Progressive rendering locked - no other processes can start');

    try {
      _isPinFetchInProgress = true;

      // Initialize timing for performance tracking
      _lastPinUpdate = DateTime.now();

      // Only start scanning animation for user-triggered updates
      if (_currentRenderingContext == RenderingContext.userTriggered) {
        _startScanningAnimation();
      }

      // Initialize empty layers first for immediate feedback
      _initializeEmptyLayers();

      // Fetch pin data (all at once, but render progressively)
      final pinData = await _getCachedOrFetchPins();

      if (pinData.isEmpty) {
        debugPrint('📍 No pin data available for progressive rendering');
        _stopScanningAnimation();
        _isPinFetchInProgress = false;

        return;
      }

      // Filter out pins that are already rendered to avoid duplicate processing
      final unrenderedPins = pinData
          .where((pin) => !_renderedPinIds.contains(pin['id']?.toString()))
          .toList();

      debugPrint('📍 🔍 Total pins: ${pinData.length}, Already rendered: ${_renderedPinIds.length}, Unrendered: ${unrenderedPins.length}');

      // If most pins are already rendered, skip progressive rendering
      if (unrenderedPins.length <= 2 && _renderedPinIds.isNotEmpty) {
        debugPrint('📍 ✅ Most pins already rendered, skipping progressive rendering');
        _stopScanningAnimation();
        _isPinFetchInProgress = false;

        // Just add the few remaining pins directly if any
        if (unrenderedPins.isNotEmpty) {
          debugPrint('📍 ➕ Adding ${unrenderedPins.length} remaining pins directly');
          await _addRemainingPinsDirectly(unrenderedPins);
        }
        return;
      }

      // Clear pending pins and reset rendered tracking for fresh start
      _pendingPins.clear();
      // For initial load, map style change, or filter change, clear rendered pins
      if (_currentRenderingContext == RenderingContext.initialLoad ||
          _currentRenderingContext == RenderingContext.mapStyleChange ||
          _currentRenderingContext == RenderingContext.filterChange) {
        _renderedPinIds.clear();
        debugPrint('📍 🔄 Cleared rendered pins for fresh ${_currentRenderingContext.name} rendering');
        // Use all pins for fresh rendering
        _pendingPins.addAll(_sortPinsByDistanceFromUser(pinData));
      } else {
        // For incremental updates, only process unrendered pins
        _pendingPins.addAll(_sortPinsByDistanceFromUser(unrenderedPins));
      }

      debugPrint(
          '📍 🚀 Added ${_pendingPins.length} pins to progressive rendering queue (sorted by distance)');
      debugPrint(
          '📍 🚀 Pin IDs: ${_pendingPins.map((p) => p['id']?.toString()).where((id) => id != null).toList()}');

      // Start progressive rendering (don't set _isPinFetchInProgress to false yet)
      _startProgressiveRendering();
    } catch (e) {
      debugPrint('📍 Error in progressive pin update: $e');
      _stopScanningAnimation();
      _isPinFetchInProgress = false;
      // CRITICAL: Reset atomic flag on error
      _isProgressiveRenderingActive = false;
      debugPrint('📍 🔓 Progressive rendering unlocked due to error');
    }
    // Note: _isPinFetchInProgress is set to false in _onProgressiveRenderingComplete()
  }

  // Helper method to add remaining pins directly without progressive rendering
  Future<void> _addRemainingPinsDirectly(List<Map<String, dynamic>> pins) async {
    if (_mapController == null || pins.isEmpty) return;

    try {
      debugPrint('📍 ➕ Adding ${pins.length} remaining pins directly');

      // Add pins to individual layer
      await _addPinBatchToIndividualLayer(pins);

      // Add glow effects
      await _addIncrementalGlowEffects(pins);

      // Update cluster layer if needed
      if (_currentZoom < _maxZoomForClustering) {
        await _addPinBatchToClusters(pins);
      }

      // Mark pins as rendered
      for (final pin in pins) {
        final pinId = pin['id']?.toString();
        if (pinId != null) {
          _renderedPinIds.add(pinId);
        }
      }

      // Ensure correct layer visibility
      final shouldCluster = _currentZoom < _maxZoomForClustering;
      await _switchLayerVisibility(shouldCluster);
      _currentlyShowingClusterLayer = shouldCluster;
      _isCurrentlyShowingClusters = shouldCluster;

      debugPrint('📍 ✅ Successfully added ${pins.length} remaining pins directly');
    } catch (e) {
      debugPrint('📍 ❌ Error adding remaining pins directly: $e');
    }
  }

  // Initialize empty layers for immediate user feedback
  Future<void> _initializeEmptyLayers() async {
    if (_mapController == null) return;

    debugPrint('📍 🏗️ Initializing empty layers...');

    // Clear existing layers first
    _clearExistingSymbols();
    _removeGlowLayers();

    // Add pin icons if not already added
    _addPinIcons();

    // Create empty individual pins layer
    _createEmptyIndividualPinsLayer();

    // Create empty cluster layer
    _createEmptyClusterLayer();

    // Initialize empty glow sources
    _initializeEmptyGlowSources();

    // DUAL LAYER: Set correct layer visibility from the start based on zoom
    final currentZoom = _mapController?.cameraPosition?.zoom ?? _currentZoom;
    final shouldCluster = currentZoom < _maxZoomForClustering;

    debugPrint('📍 🏗️ DUAL LAYER: Setting initial layer visibility based on zoom');
    debugPrint('📍 🏗️ Current zoom: $currentZoom, should cluster: $shouldCluster');

    try {
      // Set correct layer visibility from the start
      await _setLayerVisibilityImmediate(shouldCluster);
      debugPrint('📍 🏗️ Initial layer visibility set: ${shouldCluster ? 'cluster' : 'individual'}');
    } catch (e) {
      debugPrint('📍 ⚠️ Error setting initial layer visibility: $e');
    }

    _layersInitialized = true;

    // Start periodic validation monitoring to catch synchronization issues
    _startLayerValidationMonitoring();

    debugPrint(
        '📍 ✅ Empty layers initialized with validation monitoring, ready for progressive updates (showing ${shouldCluster ? 'clusters' : 'individual pins'})');
  }

  // Start progressive rendering timer
  void _startProgressiveRendering() {
    // CRITICAL: Additional atomic check to prevent race conditions
    if (_isProgressiveRenderingActive && _progressiveUpdateTimer?.isActive == true) {
      debugPrint('📍 ⚠️ Progressive rendering already active with timer running, skipping');
      return;
    }

    _progressiveUpdateTimer?.cancel();

    debugPrint(
        '📍 🔄 Starting progressive rendering timer with ${_pendingPins.length} pending pins...');
    debugPrint(
        '📍 🔄 Current state: _isPinFetchInProgress=$_isPinFetchInProgress, _isProgressiveRenderingActive=$_isProgressiveRenderingActive');

    if (_pendingPins.isEmpty) {
      debugPrint(
          '📍 ⚠️ No pending pins for progressive rendering, completing immediately');
      _onProgressiveRenderingComplete();
      return;
    }

    // Additional check to prevent duplicate processing of the same pins
    final uniquePendingPins = <String, Map<String, dynamic>>{};
    for (final pin in _pendingPins) {
      final pinId = pin['id']?.toString();
      if (pinId != null && !_renderedPinIds.contains(pinId)) {
        uniquePendingPins[pinId] = pin;
      }
    }

    // Update pending pins to only include unique, unrendered pins
    _pendingPins.clear();
    _pendingPins.addAll(uniquePendingPins.values);

    debugPrint('📍 🔍 Filtered to ${_pendingPins.length} unique unrendered pins');

    if (_pendingPins.isEmpty) {
      debugPrint(
          '📍 ✅ All pins already rendered, completing progressive rendering immediately');
      _onProgressiveRenderingComplete();
      return;
    }

    // Set the fetch in progress flag to prevent conflicts
    _isPinFetchInProgress = true;

    // Add timeout protection to prevent stuck progressive rendering
    final startTime = DateTime.now();
    const maxProgressiveRenderingDuration = Duration(minutes: 2); // 2 minute timeout

    _progressiveUpdateTimer =
        Timer.periodic(_progressiveUpdateInterval, (timer) {
      // CRITICAL FIX: Check if concurrent operations are happening that could interfere
      if (_isUpdatingIndividualSource || _isUpdatingClusterSource || _isSwitchingLayers) {
        debugPrint('📍 ⚠️ Progressive rendering paused - concurrent operations in progress');
        return; // Skip this cycle, don't cancel timer
      }

      // Check for timeout to prevent stuck progressive rendering
      if (DateTime.now().difference(startTime) > maxProgressiveRenderingDuration) {
        debugPrint('📍 ⏰ Progressive rendering timeout reached, forcing completion');
        timer.cancel();
        _onProgressiveRenderingComplete();
        return;
      }

      if (_pendingPins.isEmpty) {
        debugPrint(
            '📍 ✅ Progressive rendering timer completed, all pins processed');
        timer.cancel();
        _onProgressiveRenderingComplete();
        return;
      }

      // Check if we should continue (in case of filter changes)
      if (!mounted) {
        debugPrint('📍 ⚠️ Widget unmounted, stopping progressive rendering');
        timer.cancel();
        _isPinFetchInProgress = false;
        // CRITICAL: Reset atomic flag on unmount
        _isProgressiveRenderingActive = false;
        return;
      }

      // CRITICAL: Additional atomic flag check in timer callback
      if (!_isProgressiveRenderingActive) {
        debugPrint('📍 ⚠️ Atomic flag cleared, cancelling progressive rendering timer');
        timer.cancel();
        return;
      }

      // Process next batch of pins
      debugPrint(
          '📍 ⏱️ Processing pin batch (${_pendingPins.length} pending, ${_renderedPinIds.length} rendered)');
      _processPinBatch();
    });

    // Process first batch immediately
    debugPrint('📍 ⚡ Processing first batch immediately...');
    _processPinBatch();
  }

  // Process a batch of pins for rendering
  void _processPinBatch() async {
    if (_mapController == null || _pendingPins.isEmpty) return;

    // Take next batch of pins
    final batchSize =
        math.min(_progressiveUpdateBatchSize, _pendingPins.length);
    final batch = _pendingPins.take(batchSize).toList();
    _pendingPins.removeRange(0, batchSize);

    // Filter out pins that are already rendered to prevent duplicate processing
    final newPins = batch.where((pin) {
      final pinId = pin['id']?.toString();
      return pinId != null && !_renderedPinIds.contains(pinId);
    }).toList();

    if (newPins.isEmpty) {
      debugPrint(
          '📍 ⚡ Skipping batch - all ${batch.length} pins already rendered');
      return;
    }

    debugPrint(
        '📍 ⚡ Processing batch of ${newPins.length} NEW pins (${batch.length - newPins.length} already rendered, ${_pendingPins.length} remaining)');

    try {
      // Add only new pins to appropriate layers - MUST await to ensure pins are actually added
      await _addPinBatchToLayers(newPins);

      // Mark pins as rendered ONLY after they're successfully added to layers
      for (final pin in newPins) {
        final pinId = pin['id']?.toString();
        if (pinId != null) {
          _renderedPinIds.add(pinId);
          // Also add to _testPinSymbols to track visibility
          final lat = pin['latitude'] as double?;
          final lng = pin['longitude'] as double?;
          if (lat != null && lng != null) {
            _testPinSymbols
                .add(Symbol(pinId, SymbolOptions(geometry: LatLng(lat, lng))));
          }
        }
      }

      // CRITICAL: Do NOT switch layer visibility during progressive rendering batches
      // This prevents individual pins from disappearing when switching to cluster view
      // Layer visibility will be properly set when progressive rendering completes
      final currentZoom = _mapController?.cameraPosition?.zoom ?? _currentZoom;
      final shouldCluster = currentZoom < _maxZoomForClustering;
      debugPrint(
          '📍 ⚡ Batch processed - skipping layer visibility switch during progressive rendering (${newPins.length} pins added, ${_renderedPinIds.length} total)');
      debugPrint(
          '📍 ⚡ Target layer: ${shouldCluster ? 'clusters' : 'individual pins'} (will be set when progressive rendering completes)');

      // Update UI to show progress (less frequently to improve performance)
      if (mounted && _renderedPinIds.length % 10 == 0) {
        // Update UI every 10 pins instead of every 6
        setState(() {});
      }

      // Update pins in aura (less frequently)
      if (_renderedPinIds.length % 15 == 0) {
        // Update aura every 15 pins instead of 9
        _updatePinsInAura();
      }

      // Only animate pins for user-triggered updates, not initial loads or style changes
      // if (_currentRenderingContext == RenderingContext.userTriggered ||
      //     _currentRenderingContext == RenderingContext.incrementalUpdate) {
      //   final newPinIds = batch
      //       .map((p) => p['id']?.toString())
      //       .where((id) => id != null)
      //       .cast<String>()
      //       .toList();
      //   if (newPinIds.isNotEmpty) {
      //     if (_isPinFetchInProgress) {
      //       // Use lighter progressive loading animation during updates
      //       _animateProgressivePins(batch);
      //     } else {
      //       // Use full animation for AR-placed pins
      //       _animateNewPins(newPinIds);
      //     }
      //   }
      // }
    } catch (e) {
      debugPrint('📍 ❌ Error processing pin batch: $e');
    }
  }

  // REWRITTEN: Dual Layer Progressive Rendering - Build BOTH layers simultaneously
  Future<void> _addPinBatchToLayers(List<Map<String, dynamic>> batch) async {
    if (_mapController == null || batch.isEmpty) return;

    debugPrint('📍 🔄 DUAL LAYER: Adding ${batch.length} pins to BOTH individual AND cluster layers');

    try {
      // Get all pins rendered so far (including this batch)
      final allRenderedPins = <Map<String, dynamic>>[];

      // Add previously rendered pins
      for (final pinId in _renderedPinIds) {
        final pin = _pinCache[pinId] ?? _realPinData[pinId];
        if (pin != null) {
          allRenderedPins.add(pin);
        }
      }

      // Add new batch
      allRenderedPins.addAll(batch);

      // 1. ALWAYS update individual layer with new batch
      await _addPinBatchToIndividualLayer(batch);
      debugPrint('📍 ✅ Individual layer updated with ${batch.length} new pins');

      // 2. IMMEDIATELY update cluster layer with ALL pins (including new batch)
      await _updateClusterLayerWithAllPins(allRenderedPins);
      debugPrint('📍 ✅ Cluster layer updated with ${allRenderedPins.length} total pins');

      // 3. IMMEDIATELY set correct layer visibility based on current zoom
      final currentZoom = _mapController?.cameraPosition?.zoom ?? _currentZoom;
      final shouldCluster = currentZoom < _maxZoomForClustering;

      await _setLayerVisibilityImmediate(shouldCluster);
      debugPrint('📍 ✅ Layer visibility set: ${shouldCluster ? 'cluster' : 'individual'} (zoom: $currentZoom)');

    } catch (e) {
      debugPrint('📍 ❌ Error in dual layer progressive rendering: $e');
    }
  }

  // NEW: Update cluster layer with all pins immediately during progressive rendering
  Future<void> _updateClusterLayerWithAllPins(List<Map<String, dynamic>> allPins) async {
    if (_mapController == null || allPins.isEmpty) return;

    try {
      debugPrint('📍 🧠 IMMEDIATE: Building cluster layer with ${allPins.length} pins');

      // Calculate clusters for all pins using isolate
      final clusters = await ClusterService.instance.computeClusters(
        pins: _allPinsData,
        clusterRadius: 250.0,
        minClusterSize: 3,
      );

      debugPrint('📍 🧠 IMMEDIATE: Calculated ${clusters.length} clusters');

      // Create cluster icons FIRST before creating features
      final neededCounts = clusters
          .where((c) => c['isCluster'] == true)
          .map((c) => c['count'] as int)
          .toSet();

      if (neededCounts.isNotEmpty) {
        await _addNeededClusterIcons(neededCounts);
        debugPrint('📍 🧠 IMMEDIATE: Added cluster icons for counts: $neededCounts');
      }

      // Create GeoJSON features for cluster layer
      final features = <Map<String, dynamic>>[];

      for (final cluster in clusters) {
        final lat = cluster['latitude'] as double;
        final lng = cluster['longitude'] as double;
        final isCluster = cluster['isCluster'] == true;

        if (isCluster) {
          // Create cluster feature with number
          final count = cluster['count'] as int;
          final clusterId = cluster['id']?.toString() ?? 'cluster-${DateTime.now().millisecondsSinceEpoch}';

          features.add({
            'type': 'Feature',
            'geometry': {
              'type': 'Point',
              'coordinates': [lng, lat],
            },
            'properties': {
              'id': clusterId,
              'icon': 'cluster-icon-$count',
              'count': count,
              'isCluster': true,
              'isPlaying': false,
            },
          });
        } else {
          // Individual pin in cluster layer
          final pinData = cluster['pin'] as Map<String, dynamic>?;
          if (pinData != null) {
            final pinId = pinData['id']?.toString();
            if (pinId != null) {
              // Get pin rarity and icon
              final rarity = _getPinRarity(pinData);
              String iconImageId = 'music-pin-$rarity';

              // Check for custom skin
              if (pinData['skinDetails'] != null &&
                  pinData['skinDetails']['image'] != null) {
                final customImageId = 'pin-skin-$pinId';
                iconImageId = customImageId;
              }

              features.add({
                'type': 'Feature',
                'geometry': {
                  'type': 'Point',
                  'coordinates': [lng, lat],
                },
                'properties': {
                  'id': pinId,
                  'icon': iconImageId,
                  'isCluster': false,
                  'isPlaying': false,
                },
              });
            }
          }
        }
      }

      // Update cluster-pins-source with features
      await _mapController!.setGeoJsonSource('cluster-pins-source', {
        'type': 'FeatureCollection',
        'features': features,
      });

      debugPrint('📍 🧠 IMMEDIATE: Updated cluster-pins-source with ${features.length} features');

      // Force update cluster symbols for tap handling (bypass optimization checks)
      _forceUpdateClusterSymbols(clusters);

    } catch (e) {
      debugPrint('📍 🧠 ❌ Error updating cluster layer immediately: $e');
    }
  }

  // NEW: Force update cluster symbols bypassing optimization checks
  void _forceUpdateClusterSymbols(List<Map<String, dynamic>> clusters) {
    debugPrint('📍 🎯 FORCE: Updating cluster symbols (bypassing optimization)');

    _clusterSymbols.clear();
    _clusters.clear();

    for (final cluster in clusters) {
      if (cluster['isCluster'] == true) {
        final lat = cluster['latitude'] as double;
        final lng = cluster['longitude'] as double;

        final symbol = Symbol(
          cluster['id'],
          SymbolOptions(geometry: LatLng(lat, lng)),
        );
        _clusterSymbols.add(symbol);
        _clusters.add(cluster);
      }
    }

    debugPrint('📍 🎯 FORCE: Created ${_clusterSymbols.length} cluster symbols for tap handling');
  }

  // NEW: Set layer visibility immediately without complex state checks
  Future<void> _setLayerVisibilityImmediate(bool showClusters) async {
    if (_mapController == null) return;

    try {
      await Future.wait([
        _mapController!.setLayerVisibility('individual-pins-layer', !showClusters),
        _mapController!.setLayerVisibility('cluster-pins-layer', showClusters),
      ]);

      // Update state flags
      _currentlyShowingClusterLayer = showClusters;
      _isCurrentlyShowingClusters = showClusters;

      debugPrint('📍 🔄 IMMEDIATE: Layer visibility set - individual: ${!showClusters}, cluster: $showClusters');
    } catch (e) {
      debugPrint('📍 ❌ Error setting layer visibility immediately: $e');
    }
  }

  // Add all pins to individual pins layer (for when user zooms in from cluster view)
  // WARNING: This method clears the layer before adding pins - DO NOT use during progressive rendering!
  // Use _addPinBatchToIndividualLayer() instead for accumulative rendering
  Future<void> _addAllPinsToIndividualLayer() async {
    if (_mapController == null) return;

    try {
      // Get all rendered pins
      final allPins = <Map<String, dynamic>>[];
      for (final pinId in _renderedPinIds) {
        final pin = _pinCache[pinId] ?? _realPinData[pinId];
        if (pin != null) {
          allPins.add(pin);
        }
      }

      // Use isolate-based pin feature computation for non-blocking processing
      debugPrint(
          '📍 🔧 Full rebuild: Computing pin features for ${allPins.length} pins in isolate');
      final features =
          await PinFeatureService.instance.computePinFeatures(pins: allPins);
      debugPrint(
          '📍 🔧 Full rebuild: Isolate returned ${features.length} features');

      // Process results and handle skin images (same as batch processing)
      for (final feature in features) {
        debugPrint(
            '📍 🔧 Full rebuild: Processing feature: ${feature['properties']?['id']}');

        // Handle skin images that isolate couldn't process
        final properties = feature['properties'] as Map<String, dynamic>?;
        if (properties != null) {
          final skinImageUrl = properties['skinImageUrl'] as String?;
          final pinId = properties['id']?.toString();

          if (skinImageUrl != null &&
              skinImageUrl.isNotEmpty &&
              pinId != null) {
            try {
              // Add the skin image that the isolate couldn't add
              final customImageId = 'pin-skin-$pinId';
              final added = await _addSkinImage(
                  imageUrl: skinImageUrl, imageId: customImageId);
              if (added) {
                // Update the feature's icon to use the custom skin
                properties['icon'] = customImageId;
                debugPrint(
                    '📍 🔧 Full rebuild: Added custom skin for pin $pinId: $customImageId');
              }
            } catch (e) {
              debugPrint(
                  '📍 ⚠️ Full rebuild: Failed to add custom skin for pin $pinId: $e');
            }
          }

          // Check if this is a playing pin and update accordingly
          final isPlaying =
              _currentlyPlayingPinId != null && _currentlyPlayingPinId == pinId;
          properties['isPlaying'] = isPlaying;

          // Extract original pin data and update _realPinData for tap handling
          if (properties.containsKey('originalData')) {
            final originalData =
                properties['originalData'] as Map<String, dynamic>;
            final pinId = originalData['id']?.toString();
            if (pinId != null) {
              _realPinData[pinId] = originalData;
              debugPrint(
                  '📍 🔧 Full rebuild: Updated real pin data for: $pinId');
            }
          } else {
            debugPrint(
                '📍 ⚠️ Full rebuild: Feature missing originalData: ${properties['id']}');
          }
        }
      }

      // Update the individual pins source with all features
      await _mapController!.setGeoJsonSource(
        'individual-pins-source',
        {
          'type': 'FeatureCollection',
          'features': features,
        },
      );

      // Update layer features tracking to match the actual layer state
      _clearLayerFeatures('individual-pins-source');
      _addToLayerFeatures('individual-pins-source', features);

      debugPrint(
          '📍 ✅ Updated individual pins layer with all ${features.length} pins');

      // CRITICAL: Recreate glow effects after layer rebuild to prevent aura disappearing
      debugPrint('📍 ✨ Recreating glow effects after full layer rebuild');
      await _addOptimizedGlowCircles(allPins);
      debugPrint('📍 ✨ Glow effects recreated successfully');
    } catch (e) {
      debugPrint('📍 ❌ Error updating individual pins layer: $e');
    }
  }

  // Add pin batch to individual pins layer with enhanced error handling
  Future<void> _addPinBatchToIndividualLayer(
      List<Map<String, dynamic>> batch) async {
    if (_mapController == null || batch.isEmpty) return;

    try {
      // Get current features from memory tracking
      final currentFeatures =
          await _getCurrentLayerFeatures('individual-pins-source');
      final newFeatures = <Map<String, dynamic>>[];
      final processedPinIds = <String>{};

      // Use parallel isolate-based pin feature computation for non-blocking processing
      debugPrint(
          '📍 🔧 Computing pin features for ${batch.length} pins in parallel isolate');
      final features = await PinFeatureService.instance.computePinFeatures(
        pins: batch,
        chunkSize: 5, // Process in smaller chunks for better parallelization
      );
      debugPrint('📍 🔧 Parallel isolate returned ${features.length} features');

      // Validate that we got features for all pins
      final expectedPinIds = batch
          .map((p) => p['id']?.toString())
          .where((id) => id != null)
          .toSet();
      final actualPinIds = features
          .map((f) => f['properties']?['id']?.toString())
          .where((id) => id != null)
          .toSet();
      if (expectedPinIds.length != actualPinIds.length) {
        debugPrint(
            '📍 ⚠️ Feature count mismatch: expected ${expectedPinIds.length}, got ${actualPinIds.length}');
        debugPrint(
            '📍 ⚠️ Missing pins: ${expectedPinIds.difference(actualPinIds)}');
      }

      // Process results and handle skin images (isolate couldn't add them)
      for (final feature in features) {
        final properties = feature['properties'] as Map<String, dynamic>?;
        if (properties == null) {
          debugPrint('📍 ⚠️ Feature missing properties: ${feature['id']}');
          continue;
        }

        final pinId = properties['id']?.toString();
        if (pinId == null) {
          debugPrint('📍 ⚠️ Feature missing pin ID');
          continue;
        }

        debugPrint('📍 🔧 Processing feature: $pinId');

        // Handle skin images that isolate couldn't process
        final skinImageUrl = properties['skinImageUrl'] as String?;
        if (skinImageUrl != null && skinImageUrl.isNotEmpty) {
          try {
            // Add the skin image that the isolate couldn't add
            final customImageId = 'pin-skin-$pinId';
            final added = await _addSkinImage(
                imageUrl: skinImageUrl, imageId: customImageId);
            if (added) {
              // Update the feature's icon to use the custom skin
              properties['icon'] = customImageId;
              debugPrint(
                  '📍 🔧 Added custom skin for pin $pinId: $customImageId');
            }
          } catch (e) {
            debugPrint('📍 ⚠️ Failed to add custom skin for pin $pinId: $e');
          }
        }

        // Check if this is a playing pin and update accordingly
        final isPlaying =
            _currentlyPlayingPinId != null && _currentlyPlayingPinId == pinId;
        properties['isPlaying'] = isPlaying;

        // Extract original pin data and update _realPinData for tap handling
        if (properties.containsKey('originalData')) {
          final originalData =
              properties['originalData'] as Map<String, dynamic>;
          _realPinData[pinId] = originalData;
          debugPrint('📍 🔧 Updated real pin data for: $pinId');
        } else {
          debugPrint('📍 ⚠️ Feature missing originalData: $pinId');
        }

        newFeatures.add(feature);
        processedPinIds.add(pinId);
      }

      // Add to memory tracking
      _addToLayerFeatures('individual-pins-source', newFeatures);

      // Get all features for update
      final allFeatures =
          await _getCurrentLayerFeatures('individual-pins-source');

      // Update the source with all features
      await _mapController!.setGeoJsonSource(
        'individual-pins-source',
        {
          'type': 'FeatureCollection',
          'features': allFeatures,
        },
      );

      debugPrint(
          '📍 ✅ Added ${newFeatures.length} pins to individual layer (total: ${allFeatures.length})');
      debugPrint('📍 ✅ Processed pin IDs: ${processedPinIds.toList()}');

      // CRITICAL: Add glow effects incrementally for this batch so aura persists in cluster view!
      debugPrint(
          '📍 ✨ Adding incremental glow effects for batch of ${batch.length} pins');
      await _addIncrementalGlowEffects(batch);
      debugPrint('📍 ✨ Incremental glow effects added for batch');
    } catch (e) {
      debugPrint('📍 ❌ Error adding batch to individual layer: $e');
      // Try to recover by processing pins one by one
      for (final pin in batch) {
        try {
          await _addPinBatchToIndividualLayer([pin]);
        } catch (singlePinError) {
          debugPrint(
              '📍 ❌ Failed to process single pin ${pin['id']}: $singlePinError');
        }
      }
    }
  }

  // Add pin batch to cluster layer (recalculate clusters)
  Future<void> _addPinBatchToClusters(List<Map<String, dynamic>> batch) async {
    if (_mapController == null) return;

    try {
      // Get all pins rendered so far plus new batch
      final allRenderedPins = <Map<String, dynamic>>[];

      // Add previously rendered pins
      for (final pinId in _renderedPinIds) {
        final pin = _pinCache[pinId] ?? _realPinData[pinId];
        if (pin != null) {
          allRenderedPins.add(pin);
        }
      }

      // Add new batch
      allRenderedPins.addAll(batch);

      // Use isolate-based clustering for non-blocking computation
      final clusters = await ClusterService.instance.computeClusters(
        pins: _allPinsData,
        clusterRadius: 250.0, // 100 meters clustering radius
        minClusterSize: 3,
      );

      debugPrint(
          '📍 🔍 About to update cluster layer with ${clusters.length} items');
      debugPrint(
          '📍 🔍 - Clusters: ${clusters.where((c) => c['isCluster'] == true).length}');
      debugPrint(
          '📍 🔍 - Individual pins: ${clusters.where((c) => !c['isCluster']).length}');

      // Create cluster icons for any new cluster sizes
      final neededCounts = clusters
          .where((c) => c['isCluster'] == true)
          .map((c) => c['count'] as int)
          .toSet();
      await _addNeededClusterIcons(neededCounts);

      // Update cluster layer in parallel with progressive rendering
      // Only force full update for specific contexts that require fresh rendering
      final forceFullUpdate = _currentRenderingContext == RenderingContext.initialLoad ||
          _currentRenderingContext == RenderingContext.mapStyleChange ||
          _currentRenderingContext == RenderingContext.filterChange;

      await _updateClusterLayerFeatures(clusters, forceFullUpdate: forceFullUpdate);

      debugPrint('📍 🔍 Cluster layer updated with forceFullUpdate: $forceFullUpdate (context: ${_currentRenderingContext.name})');

      // Update cluster symbols for tap handling - only if we have meaningful changes
      final currentClusterCount =
          clusters.where((c) => c['isCluster'] == true).length;
      final previousClusterCount = _clusterSymbols.length;

      if (currentClusterCount != previousClusterCount ||
          currentClusterCount > 0) {
        _updateClusterSymbolsFromFeatures(clusters);
      } else {
        debugPrint(
            '📍 🎯 Skipping cluster symbol update - no cluster changes detected');
      }

      debugPrint(
          '📍 ✅ Updated cluster layer with ${batch.length} new pins (total pins: ${allRenderedPins.length}, clusters: ${clusters.where((c) => c['isCluster'] == true).length})');
    } catch (e) {
      debugPrint('📍 ❌ Error adding batch to cluster layer: $e');
    }
  }

  // Force rebuild cluster layer with all currently rendered pins
  Future<void> _rebuildClusterLayerWithAllPins() async {
    if (_mapController == null || _renderedPinIds.isEmpty) return;

    debugPrint(
        '📍 🔄 Force rebuilding cluster layer with ${_renderedPinIds.length} rendered pins');

    try {
      // Get all rendered pins
      final allRenderedPins = <Map<String, dynamic>>[];
      for (final pinId in _renderedPinIds) {
        final pin = _pinCache[pinId] ?? _realPinData[pinId];
        if (pin != null) {
          allRenderedPins.add(pin);
        }
      }

      if (allRenderedPins.isNotEmpty) {
        // Create clusters for all pins using isolate
        final clusters = await ClusterService.instance.computeClusters(
          pins: _allPinsData,
          clusterRadius: 250.0,
          minClusterSize: 3,
        );

        // Update cluster layer with full rebuild
        await _updateClusterLayerFeatures(clusters, forceFullUpdate: true);
        _updateClusterSymbolsFromFeatures(clusters);

        debugPrint(
            '📍 ✅ Cluster layer rebuilt with ${clusters.where((c) => c['isCluster'] == true).length} clusters from ${allRenderedPins.length} pins');
      }
    } catch (e) {
      debugPrint('📍 ❌ Error rebuilding cluster layer: $e');
    }
  }

  // Optimized cluster rebuilding that avoids triggering progressive rendering
  Future<void> _rebuildClusterLayerWithAllPinsOptimized() async {
    if (_mapController == null || _renderedPinIds.isEmpty) return;

    debugPrint(
        '📍 🔄 Optimized cluster rebuild with ${_renderedPinIds.length} rendered pins');

    if (!_isRebuildingClusters) {
      _isRebuildingClusters = true;
      try {
        // Get all rendered pins
        final allRenderedPins = <Map<String, dynamic>>[];
        for (final pinId in _renderedPinIds) {
          final pin = _pinCache[pinId] ?? _realPinData[pinId];
          if (pin != null) {
            allRenderedPins.add(pin);
          }
        }

        if (allRenderedPins.isNotEmpty) {
          // Create clusters for all pins using isolate
          final clusters = await ClusterService.instance.computeClusters(
            pins: _allPinsData,
            clusterRadius: 250.0,
            minClusterSize: 3,
          );

          // Update cluster layer without triggering progressive rendering
          await _updateClusterLayerFeatures(clusters, forceFullUpdate: true);

          // Update cluster symbols for tap handling
          _updateClusterSymbolsFromFeatures(clusters);

          final clusterCount =
              clusters.where((c) => c['isCluster'] == true).length;
          debugPrint(
              '📍 ✅ Optimized cluster rebuild complete: $clusterCount clusters');
        }
      } catch (e) {
        debugPrint('📍 ❌ Error in optimized cluster rebuild: $e');
      } finally {
        _isRebuildingClusters = false;
      }
    }
  }

  // Called when progressive rendering is complete
  Future<void> _onProgressiveRenderingComplete() async {
    debugPrint(
        '📍 🎉 Progressive rendering complete! Rendered ${_renderedPinIds.length} pins');
    debugPrint('📍 🎉 Rendered pin IDs: ${_renderedPinIds.toList()}');
    debugPrint(
        '📍 🎉 Cache size: ${_pinCache.length}, Real pin data size: ${_realPinData.length}');

    // Show snackbar if no pins were rendered
    if (_renderedPinIds.isEmpty && _pinCache.isEmpty) {
      _showNoPinsFoundSnackbar();
    }

    // Stop scanning animation
    _stopScanningAnimation();

    // Configure symbol settings
    _configureSymbolSettings();

    // Start animations
    _startOptimizedAnimations();

    // Mark initial load as complete
    if (_isInitialPinLoad) {
      _isInitialPinLoad = false;
      debugPrint('📍 Initial pin load completed with progressive rendering');
    }

    // Final aura update
    _updatePinsInAura();

    // Validate final state
    _validatePinState();

    // Ensure all pins from cache are rendered
    final expectedPinCount = _pinCache.length;
    final actualPinCount = _renderedPinIds.length;
    if (expectedPinCount != actualPinCount) {
      debugPrint(
          '📍 ⚠️ Pin count mismatch: expected $expectedPinCount, rendered $actualPinCount');
      debugPrint(
          '📍 ⚠️ Missing pins: ${_pinCache.keys.where((id) => !_renderedPinIds.contains(id)).toList()}');

      // Try to render any missing pins
      final missingPins = <Map<String, dynamic>>[];
      for (final pinId in _pinCache.keys) {
        if (!_renderedPinIds.contains(pinId)) {
          final pin = _pinCache[pinId];
          if (pin != null) {
            missingPins.add(pin);
          }
        }
      }

      if (missingPins.isNotEmpty) {
        debugPrint('📍 🔄 Rendering ${missingPins.length} missing pins');
        await _addPinBatchToLayers(missingPins);

        // Mark missing pins as rendered
        for (final pin in missingPins) {
          final pinId = pin['id']?.toString();
          if (pinId != null) {
            _renderedPinIds.add(pinId);
          }
        }

        debugPrint(
            '📍 ✅ Missing pins rendered, total: ${_renderedPinIds.length}');
      }
    } else {
      debugPrint(
          '📍 ✅ Pin count validation passed: $actualPinCount pins rendered');
    }

    // Update performance stats
    if (_lastPinUpdate != null) {
      final updateTime =
          DateTime.now().difference(_lastPinUpdate!).inMilliseconds;
      debugPrint('📍 Progressive rendering completed in ${updateTime}ms');
    } else {
      debugPrint(
          '📍 Progressive rendering completed (no timing data available)');
    }

    // Reset fetch progress flag and cancel timer to ensure clean state
    _isPinFetchInProgress = false;
    _progressiveUpdateTimer?.cancel();
    _progressiveUpdateTimer = null;

    // CRITICAL: Reset atomic flag to allow new progressive rendering processes
    _isProgressiveRenderingActive = false;
    debugPrint('📍 🔓 Progressive rendering unlocked - new processes can start');

    // Clear pending pins to ensure no leftover state
    _pendingPins.clear();

    debugPrint('📍 🧹 Progressive rendering state fully reset: fetchInProgress=$_isPinFetchInProgress, timerActive=${_progressiveUpdateTimer?.isActive}, pendingPins=${_pendingPins.length}, active=$_isProgressiveRenderingActive');

    // Handle deferred layer switching if it was requested during progressive rendering
    if (_pendingClusterDisplay != null) {
      debugPrint(
          '📍 🚀 Executing deferred layer switch after progressive rendering completion');
      debugPrint('📍 🚀 Deferred state: shouldShowClusters=$_pendingClusterDisplay');
      
      final shouldShowClusters = _pendingClusterDisplay!;
      _pendingClusterDisplay = null; // Clear the flag

      // Switch to the deferred layer state now that progressive rendering is complete
      debugPrint(
          '📍 🚀 Switching to ${shouldShowClusters ? 'cluster' : 'individual'} layer (deferred from progressive rendering)');
      await _synchronizeLayerVisibility(shouldShowClusters, 'deferred_switch');
      _currentlyShowingClusterLayer = shouldShowClusters;
      _isCurrentlyShowingClusters = shouldShowClusters;
      
      debugPrint('📍 ✅ Deferred layer switch completed successfully');
      return; // Exit early since we've handled the deferred display
    }

    // Check if we're currently showing clusters and they're working correctly
    final currentZoom = _mapController?.cameraPosition?.zoom ?? _currentZoom;
    final shouldCluster = currentZoom < _maxZoomForClustering;
    final currentlyShowingClusters = _currentlyShowingClusterLayer;

    debugPrint(
        '📍 🎯 Progressive rendering completion state: shouldCluster=$shouldCluster, currentlyShowingClusters=$currentlyShowingClusters');
    debugPrint(
        '📍 🎯 Current zoom from controller: $currentZoom, max zoom for clustering: $_maxZoomForClustering');
    debugPrint(
        '📍 🎯 Rendered pins: ${_renderedPinIds.length}, cache size: ${_pinCache.length}');

    // For progressive rendering, we need to ensure proper layer visibility regardless of current state
    // because the layers may have been populated incrementally
    debugPrint(
        '📍 🔄 Progressive rendering complete - ensuring proper layer visibility');
    debugPrint(
        '📍 🔄 Current zoom: $currentZoom, should cluster: $shouldCluster, currently showing clusters: $currentlyShowingClusters');

    try {
      // DUAL LAYER: Both layers are already built during progressive rendering
      debugPrint('📍 ✅ Progressive rendering complete - both layers already built during rendering');

      final currentZoom = _mapController?.cameraPosition?.zoom ?? _currentZoom;
      final shouldCluster = currentZoom < _maxZoomForClustering;

      // Ensure correct layer visibility (should already be set, but verify)
      await _setLayerVisibilityImmediate(shouldCluster);
      debugPrint('📍 ✅ Final layer visibility confirmed: ${shouldCluster ? 'cluster' : 'individual'}');

      // Validate cluster state if showing clusters
      if (shouldCluster) {
        final clusterCount = _clusters.length;
        final individualPinCount = _renderedPinIds.length;
        debugPrint('📍 📊 Cluster validation: $clusterCount clusters from $individualPinCount pins');
      }

    } catch (e) {
      debugPrint('📍 ❌ Error in progressive rendering completion: $e');
    }
  }

  // Ensure only the individual layer is populated (for when clusters are already working)
  Future<void> _ensureIndividualLayerPopulated() async {
    if (_mapController == null || _renderedPinIds.isEmpty) return;

    debugPrint(
        '📍 🔄 Ensuring individual layer is populated with ${_renderedPinIds.length} pins');

    try {
      // Get all rendered pins
      final allRenderedPins = <Map<String, dynamic>>[];
      for (final pinId in _renderedPinIds) {
        final pin = _pinCache[pinId] ?? _realPinData[pinId];
        if (pin != null) {
          allRenderedPins.add(pin);
        }
      }

      if (allRenderedPins.isNotEmpty) {
        // Use accumulative method to avoid clearing existing pins
        await _addPinBatchToIndividualLayer(allRenderedPins);
        debugPrint(
            '📍 ✅ Individual layer populated: ${allRenderedPins.length} pins');
      }
    } catch (e) {
      debugPrint('📍 ❌ Error ensuring individual layer population: $e');
    }
  }

  // Toggle progressive rendering on/off
  void toggleProgressiveRendering({bool? enabled}) {
    _isProgressiveRenderingEnabled = enabled ?? !_isProgressiveRenderingEnabled;
    debugPrint(
        '📍 Progressive rendering ${_isProgressiveRenderingEnabled ? 'enabled' : 'disabled'}');

    // Cancel current progressive rendering if disabled
    if (!_isProgressiveRenderingEnabled) {
      _progressiveUpdateTimer?.cancel();
      _pendingPins.clear();
    }
  }

  // Strategic clustering data
  Map<String, dynamic> _clusteringStrategy = {};
  Set<String> _pinsToHideInIndividualLayer = {};
  List<Map<String, dynamic>> _preCalculatedClusters = [];

  // Pre-calculate clustering strategy before progressive rendering starts
  Future<void> _preCalculateClusteringStrategy(List<Map<String, dynamic>> pins) async {
    if (_mapController == null || pins.isEmpty) return;

    final currentZoom = _mapController?.cameraPosition?.zoom ?? _currentZoom;
    final shouldCluster = currentZoom < _maxZoomForClustering;

    debugPrint('📍 🧠 Pre-calculating clustering strategy for ${pins.length} pins (shouldCluster: $shouldCluster)');

    // ALWAYS calculate clusters for the cluster layer, regardless of zoom level
    // The layer visibility switching will control which layer is shown

    try {
      // Calculate clusters using all pins that will be rendered
      final allPinsForClustering = <Map<String, dynamic>>[];

      // Include already rendered pins
      for (final pinId in _renderedPinIds) {
        final pin = _pinCache[pinId] ?? _realPinData[pinId];
        if (pin != null) {
          allPinsForClustering.add(pin);
        }
      }

      // Include new pins to be rendered
      allPinsForClustering.addAll(pins);

      debugPrint('📍 🧠 Computing clusters for ${allPinsForClustering.length} total pins');

      // Use isolate-based clustering for performance
      final clusters = await ClusterService.instance.computeClusters(
        pins: _allPinsData,
        clusterRadius: 250.0, // 100 meters clustering radius
        minClusterSize: 3,
      );

      _preCalculatedClusters = clusters;

      // Determine which individual pins should be hidden (because they're part of clusters)
      _pinsToHideInIndividualLayer.clear();

      for (final cluster in clusters) {
        final isCluster = cluster['isCluster'] == true;
        if (isCluster) {
          final clusterPins = cluster['pins'] as List<dynamic>? ?? [];
          for (final clusterPin in clusterPins) {
            final pinId = clusterPin['id']?.toString();
            if (pinId != null) {
              _pinsToHideInIndividualLayer.add(pinId);
            }
          }
        }
      }

      debugPrint('📍 🧠 Clustering strategy calculated:');
      debugPrint('📍 🧠 - ${clusters.where((c) => c['isCluster'] == true).length} clusters');
      debugPrint('📍 🧠 - ${clusters.where((c) => c['isCluster'] != true).length} individual pins');
      debugPrint('📍 🧠 - ${_pinsToHideInIndividualLayer.length} pins to hide in individual layer');

    } catch (e) {
      debugPrint('📍 ❌ Error pre-calculating clustering strategy: $e');
      // Fallback: show all pins individually
      _pinsToHideInIndividualLayer.clear();
      _preCalculatedClusters.clear();
    }
  }

  // Build cluster layer from pre-calculated clusters
  Future<void> _buildClusterLayerFromPreCalculated() async {
    if (_mapController == null || _preCalculatedClusters.isEmpty) {
      debugPrint('📍 🧠 ❌ Cannot build cluster layer: mapController=${_mapController != null}, preCalculatedClusters=${_preCalculatedClusters.length}');
      return;
    }

    try {
      debugPrint('📍 🧠 🔨 Building cluster layer with ${_preCalculatedClusters.length} pre-calculated clusters');

      // Convert all pre-calculated clusters to features manually
      final features = <Map<String, dynamic>>[];

      for (final cluster in _preCalculatedClusters) {
        final isCluster = cluster['isCluster'] == true;

        if (isCluster) {
          // Create cluster feature
          final clusterPins = cluster['pins'] as List<dynamic>? ?? [];
          if (clusterPins.isNotEmpty) {
            final centerLat = cluster['latitude'] as double?;
            final centerLng = cluster['longitude'] as double?;
            final count = clusterPins.length;

            if (centerLat != null && centerLng != null) {
              features.add({
                'type': 'Feature',
                'properties': {
                  'cluster': true,
                  'cluster_id': cluster['id'] ?? 'cluster_${features.length}',
                  'point_count': count,
                  'point_count_abbreviated': count > 999 ? '${(count / 1000).toStringAsFixed(1)}k' : count.toString(),
                },
                'geometry': {
                  'type': 'Point',
                  'coordinates': [centerLng, centerLat],
                },
              });
            }
          }
        }
      }

      if (features.isNotEmpty) {
        // Update cluster layer with all cluster features
        await _mapController!.setGeoJsonSource('cluster-source', {
          'type': 'FeatureCollection',
          'features': features,
        });

        debugPrint('📍 🧠 ✅ Built cluster layer with ${features.length} cluster features');
      } else {
        debugPrint('📍 🧠 ⚠️ No valid cluster features created from pre-calculated clusters');
      }
    } catch (e) {
      debugPrint('📍 🧠 ❌ Error building cluster layer from pre-calculated clusters: $e');
    }
  }

  // Build cluster layer from all rendered pins (not just pre-calculated clusters)
  Future<void> _buildClusterLayerFromAllPins() async {
    if (_mapController == null || _renderedPinIds.isEmpty) {
      debugPrint('📍 🧠 ❌ Cannot build cluster layer: mapController=${_mapController != null}, renderedPins=${_renderedPinIds.length}');
      return;
    }

    try {
      debugPrint('📍 🧠 🔨 Building cluster layer with ALL ${_renderedPinIds.length} rendered pins');

      // Get all rendered pins from cache
      final allRenderedPins = <Map<String, dynamic>>[];
      for (final pinId in _renderedPinIds) {
        final pinData = _pinCache[pinId];
        if (pinData != null) {
          allRenderedPins.add(pinData);
        }
      }

      if (allRenderedPins.isEmpty) {
        debugPrint('📍 🧠 ⚠️ No pin data found in cache for rendered pins');
        debugPrint('📍 🧠 ⚠️ Rendered pin IDs: ${_renderedPinIds.toList()}');
        debugPrint('📍 🧠 ⚠️ Pin cache keys: ${_pinCache.keys.toList()}');
        return;
      }

      debugPrint('📍 🧠 ✅ Found ${allRenderedPins.length} pins in cache for clustering');

      // Calculate clusters for all pins using the cluster service
      final clusters = await ClusterService.instance.computeClusters(
        pins: _allPinsData,
        clusterRadius: 250.0, // 100 meters clustering radius
        minClusterSize: 3,
      );

      debugPrint('📍 🧠 📊 Calculated ${clusters.length} clusters from ${allRenderedPins.length} pins');

      // Convert clusters to features
      final features = <Map<String, dynamic>>[];

      for (final cluster in clusters) {
        final isCluster = cluster['isCluster'] == true;

        if (isCluster) {
          // Create cluster feature
          final clusterPins = cluster['pins'] as List<dynamic>? ?? [];
          if (clusterPins.isNotEmpty) {
            final centerLat = cluster['latitude'] as double?;
            final centerLng = cluster['longitude'] as double?;
            final count = clusterPins.length;

            if (centerLat != null && centerLng != null) {
              features.add({
                'type': 'Feature',
                'properties': {
                  'cluster': true,
                  'cluster_id': cluster['id'] ?? 'cluster_${features.length}',
                  'point_count': count,
                  'point_count_abbreviated': count > 999 ? '${(count / 1000).toStringAsFixed(1)}k' : count.toString(),
                },
                'geometry': {
                  'type': 'Point',
                  'coordinates': [centerLng, centerLat],
                },
              });
            }
          }
        } else {
          // Individual pin - add to cluster layer as single pin
          final pinData = cluster['pin'] as Map<String, dynamic>?;
          if (pinData != null) {
            final lat = pinData['latitude'] as double?;
            final lng = pinData['longitude'] as double?;
            final pinId = pinData['id']?.toString();

            if (lat != null && lng != null && pinId != null) {
              // Get pin rarity and icon
              final rarity = _getPinRarity(pinData);
              String iconImageId = 'music-pin-$rarity';

              // Check for custom skin
              if (pinData['skinDetails'] != null &&
                  pinData['skinDetails']['image'] != null) {
                final customImageId = 'pin-skin-$pinId';
                iconImageId = customImageId;
              }

              features.add({
                'type': 'Feature',
                'properties': {
                  'id': pinId,
                  'icon': iconImageId,
                  'isCluster': false,
                  'isPlaying': false,
                },
                'geometry': {
                  'type': 'Point',
                  'coordinates': [lng, lat],
                },
              });
            }
          }
        }
      }

      // Update the correct cluster pins source
      try {
        await _mapController!.setGeoJsonSource('cluster-pins-source', {
          'type': 'FeatureCollection',
          'features': features,
        });
        debugPrint('📍 🧠 ✅ Updated cluster-pins-source with ${features.length} features');
      } catch (e) {
        debugPrint('📍 🧠 ❌ Error updating cluster-pins-source: $e');
      }

      debugPrint('📍 🧠 ✅ Built cluster layer with ${features.length} features (clusters + individual pins)');

      // Add needed cluster icons first
      final neededCounts = clusters
          .where((c) => c['isCluster'] == true)
          .map((c) => c['count'] as int)
          .toSet();
      await _addNeededClusterIcons(neededCounts);
      debugPrint('📍 🧠 ✅ Added cluster icons for counts: $neededCounts');

      // Now create the visual symbols for clusters
      _updateClusterSymbolsFromFeatures(clusters);
      debugPrint('📍 🧠 ✅ Created visual cluster symbols');

    } catch (e) {
      debugPrint('📍 🧠 ❌ Error building cluster layer from all pins: $e');
    }
  }

  // Add relevant clusters to cluster layer based on current batch
  Future<void> _addRelevantClustersToLayer(List<Map<String, dynamic>> batch) async {
    if (_mapController == null || _preCalculatedClusters.isEmpty) {
      debugPrint('📍 🧠 ❌ Cannot add clusters: mapController=${_mapController != null}, preCalculatedClusters=${_preCalculatedClusters.length}');
      return;
    }

    try {
      debugPrint('📍 🧠 🔍 Adding relevant clusters for batch of ${batch.length} pins');

      // Find clusters that contain pins from the current batch
      final batchPinIds = batch
          .map((pin) => pin['id']?.toString())
          .where((id) => id != null)
          .toSet();

      debugPrint('📍 🧠 🔍 Batch pin IDs: ${batchPinIds.take(5).toList()}... (${batchPinIds.length} total)');

      final relevantClusters = <Map<String, dynamic>>[];

      for (final cluster in _preCalculatedClusters) {
        final isCluster = cluster['isCluster'] == true;

        if (isCluster) {
          // Check if this cluster contains any pins from the current batch
          final clusterPins = cluster['pins'] as List<dynamic>? ?? [];
          final hasRelevantPin = clusterPins.any((clusterPin) {
            final pinId = clusterPin['id']?.toString();
            return pinId != null && batchPinIds.contains(pinId);
          });

          if (hasRelevantPin) {
            relevantClusters.add(cluster);
          }
        } else {
          // Individual pin - check if it's in the current batch
          final pinId = cluster['id']?.toString();
          if (pinId != null && batchPinIds.contains(pinId)) {
            relevantClusters.add(cluster);
          }
        }
      }

      debugPrint('📍 🧠 🔍 Found ${relevantClusters.length} relevant clusters from ${_preCalculatedClusters.length} total');

      if (relevantClusters.isNotEmpty) {
        debugPrint('📍 🧠 ✅ Adding ${relevantClusters.length} relevant clusters/pins to cluster layer');

        // Convert clusters to features manually
        final features = <Map<String, dynamic>>[];

        for (final cluster in relevantClusters) {
          final isCluster = cluster['isCluster'] == true;

          if (isCluster) {
            // Create cluster feature
            final clusterId = cluster['id']?.toString() ?? 'cluster-${DateTime.now().millisecondsSinceEpoch}';
            final lat = cluster['latitude'] as double;
            final lng = cluster['longitude'] as double;
            final count = cluster['count'] as int;

            features.add({
              'type': 'Feature',
              'geometry': {
                'type': 'Point',
                'coordinates': [lng, lat],
              },
              'properties': {
                'id': clusterId,
                'count': count,
                'icon': 'cluster-icon-$count',
                'isCluster': true,
              },
            });
          } else {
            // Create individual pin feature
            final pinData = cluster['pin'] as Map<String, dynamic>;
            final pinId = pinData['id']?.toString();
            final lat = pinData['latitude'] as double;
            final lng = pinData['longitude'] as double;

            if (pinId != null) {
              features.add({
                'type': 'Feature',
                'geometry': {
                  'type': 'Point',
                  'coordinates': [lng, lat],
                },
                'properties': {
                  'id': pinId,
                  'icon': 'pin-icon',
                  'isCluster': false,
                },
              });
            }
          }
        }

        // Add to memory tracking
        _addToLayerFeatures('cluster-pins-source', features);

        // Update the cluster layer
        final allClusterFeatures = await _getCurrentLayerFeatures('cluster-pins-source');
        await _mapController!.setGeoJsonSource(
          'cluster-pins-source',
          {
            'type': 'FeatureCollection',
            'features': allClusterFeatures,
          },
        );

        debugPrint('📍 ✅ Added ${features.length} cluster features (total: ${allClusterFeatures.length})');
      } else {
        debugPrint('📍 🧠 No relevant clusters found for current batch');
      }
    } catch (e) {
      debugPrint('📍 ❌ Error adding relevant clusters to layer: $e');
    }
  }

  // Check if progressive rendering is recommended based on pin count
  bool shouldUseProgressiveRendering(int pinCount) {
    final result = _isProgressiveRenderingEnabled &&
        pinCount >= 15; // Use progressive rendering for 15+ pins
    debugPrint(
        '📍 shouldUseProgressiveRendering: enabled=$_isProgressiveRenderingEnabled, pinCount=$pinCount, threshold=15, result=$result');
    return result;
  }

  // Get progressive rendering status for debugging
  Map<String, dynamic> getProgressiveRenderingStatus() {
    return {
      'enabled': _isProgressiveRenderingEnabled,
      'isActive': _progressiveUpdateTimer?.isActive ?? false,
      'atomicLock': _isProgressiveRenderingActive,
      'pendingPins': _pendingPins.length,
      'renderedPins': _renderedPinIds.length,
      'batchSize': _progressiveUpdateBatchSize,
      'updateInterval': _progressiveUpdateInterval.inMilliseconds,
      'isPinFetchInProgress': _isPinFetchInProgress,
      'totalPins': _pendingPins.length + _renderedPinIds.length,
      'currentZoom': _currentZoom,
      'shouldCluster': _currentZoom < _maxZoomForClustering,
      'currentlyShowingClusters': _isCurrentlyShowingClusters,
      'layersInitialized': _layersInitialized,
      'cacheSize': _pinCache.length,
      'realPinDataSize': _realPinData.length,
    };
  }

  // Force reset progressive rendering state (for debugging stuck states)
  void forceResetProgressiveRendering() {
    debugPrint('📍 🚨 Force resetting progressive rendering state');
    _progressiveUpdateTimer?.cancel();
    _progressiveUpdateTimer = null;
    _isPinFetchInProgress = false;
    _pendingPins.clear();
    _pendingClusterDisplay = null;
    // CRITICAL: Reset atomic flag
    _isProgressiveRenderingActive = false;
    debugPrint('📍 ✅ Progressive rendering state force reset complete (including atomic flag)');
  }

  // Enhanced layer synchronization to prevent pin/glow visibility mismatches
  Future<void> _synchronizeLayerVisibility(bool showClusters, String context) async {
    if (_mapController == null) return;

    debugPrint('📍 🔄 Synchronizing layer visibility: showClusters=$showClusters, context=$context');

    try {
      // Always update pin layers first
      await Future.wait([
        _mapController!.setLayerVisibility('individual-pins-layer', !showClusters),
        _mapController!.setLayerVisibility('cluster-pins-layer', showClusters),
      ]);

      // Then synchronize glow layers
      if (showClusters) {
        // Show cluster glow layers and keep individual glow for persistent aura
        await Future.wait([
          _mapController!.setLayerVisibility('cluster-glow-fill-0', true),
          _mapController!.setLayerVisibility('cluster-glow-fill-1', true),
          _mapController!.setLayerVisibility('individual-glow-fill-0', true),
          _mapController!.setLayerVisibility('individual-glow-fill-1', true),
          _mapController!.setLayerVisibility('individual-aura-border-layer', true),
          _mapController!.setLayerVisibility('individual-glow-pulse-layer', true),
        ]);
      } else {
        // Hide cluster glow layers and ensure individual glow layers are visible
        await Future.wait([
          _mapController!.setLayerVisibility('cluster-glow-fill-0', false),
          _mapController!.setLayerVisibility('cluster-glow-fill-1', false),
          _mapController!.setLayerVisibility('individual-glow-fill-0', true),
          _mapController!.setLayerVisibility('individual-glow-fill-1', true),
          _mapController!.setLayerVisibility('individual-aura-border-layer', true),
          _mapController!.setLayerVisibility('individual-glow-pulse-layer', true),
        ]);
      }

      debugPrint('📍 ✅ Layer visibility synchronized successfully for $context');
    } catch (e) {
      debugPrint('📍 ❌ Error synchronizing layer visibility: $e');
      rethrow;
    }
  }

  // Emergency recovery when pin layer becomes hidden while glow layers are visible
  Future<void> _emergencyPinLayerRecovery() async {
    if (_mapController == null) return;

    debugPrint('📍 🚨 Emergency pin layer recovery initiated');

    try {
      // Check if glow layers are visible but pin layer is hidden
      final currentZoom = _mapController?.cameraPosition?.zoom ?? _currentZoom;
      final shouldShowClusters = currentZoom < _maxZoomForClustering;

      // Force pin layer visibility to match glow layer state
      if (!shouldShowClusters) {
        // Individual view: ensure individual pins are visible
        await _mapController!.setLayerVisibility('individual-pins-layer', true);
        await _mapController!.setLayerVisibility('cluster-pins-layer', false);
        debugPrint('📍 🚨 Emergency recovery: Restored individual pins layer visibility');
      } else {
        // Cluster view: ensure cluster pins are visible
        await _mapController!.setLayerVisibility('individual-pins-layer', false);
        await _mapController!.setLayerVisibility('cluster-pins-layer', true);
        debugPrint('📍 🚨 Emergency recovery: Restored cluster pins layer visibility');
      }

      // Update state flags
      _currentlyShowingClusterLayer = shouldShowClusters;
      _isCurrentlyShowingClusters = shouldShowClusters;

      debugPrint('📍 ✅ Emergency pin layer recovery completed');
    } catch (e) {
      debugPrint('📍 ❌ Emergency pin layer recovery failed: $e');
    }
  }

  // Periodic validation to detect and fix pin/glow layer synchronization issues
  Timer? _layerValidationTimer;
  bool _isValidationInProgress = false; // Prevent concurrent validation

  void _startLayerValidationMonitoring() {
    _layerValidationTimer?.cancel();
    // Increase validation interval to reduce interference
    _layerValidationTimer = Timer.periodic(const Duration(seconds: 15), (_) {
      if (mounted && _mapController != null && _isMapReady && _layersInitialized && !_isValidationInProgress) {
        _validatePinGlowSynchronization();
      }
    });
  }

  void _stopLayerValidationMonitoring() {
    _layerValidationTimer?.cancel();
    _layerValidationTimer = null;
  }

  Future<void> _validatePinGlowSynchronization() async {
    if (_mapController == null || !_layersInitialized || _isValidationInProgress) return;

    // Prevent concurrent validation runs
    _isValidationInProgress = true;

    try {
      // Skip validation if other operations are in progress
      if (_isSwitchingLayers || _isProgressiveRenderingActive || _isPinFetchInProgress || _progressiveUpdateTimer?.isActive == true) {
        debugPrint('📍 🔍 Skipping validation - other operations in progress');
        return;
      }

      final currentZoom = _mapController?.cameraPosition?.zoom ?? _currentZoom;
      final shouldShowClusters = currentZoom < _maxZoomForClustering;

      // Only correct if there's a clear mismatch and no operations are pending
      if (_currentlyShowingClusterLayer != shouldShowClusters) {
        debugPrint('📍 🔍 Validation detected layer mismatch: expected clusters=$shouldShowClusters, current=$_currentlyShowingClusterLayer');

        // Use a gentler approach - just update the visibility without full synchronization
        try {
          await Future.wait([
            _mapController!.setLayerVisibility('individual-pins-layer', !shouldShowClusters),
            _mapController!.setLayerVisibility('cluster-pins-layer', shouldShowClusters),
          ]);

          _currentlyShowingClusterLayer = shouldShowClusters;
          _isCurrentlyShowingClusters = shouldShowClusters;

          debugPrint('📍 ✅ Validation gently corrected layer visibility');
        } catch (e) {
          debugPrint('📍 ⚠️ Gentle validation correction failed: $e');
        }
      }
    } catch (e) {
      debugPrint('📍 ⚠️ Layer validation error: $e');
    } finally {
      _isValidationInProgress = false;
    }
  }

  // Helper methods for progressive rendering

  // Create empty individual pins layer
  Future<void> _createEmptyIndividualPinsLayer() async {
    if (_mapController == null) return;

    try {
      // Remove existing layer/source
      await _mapController!
          .removeLayer('individual-pins-layer')
          .catchError((_) {});
      await _mapController!
          .removeSource('individual-pins-source')
          .catchError((_) {});

      // Add empty source
      await _mapController!.addSource(
        'individual-pins-source',
        const GeojsonSourceProperties(
          data: {
            'type': 'FeatureCollection',
            'features': [],
          },
        ),
      );

      // Add layer
      await _mapController!.addSymbolLayer(
        'individual-pins-source',
        'individual-pins-layer',
        const SymbolLayerProperties(
          iconImage: ['get', 'icon'],
          iconSize: [
            'case',
            ['get', 'isPlaying'],
            1.96,
            1.4
          ],
          iconAnchor: 'bottom',
          iconAllowOverlap: true,
          iconIgnorePlacement: true,
        ),
      );

      debugPrint('📍 ✅ Created empty individual pins layer');
    } catch (e) {
      debugPrint('📍 ❌ Error creating empty individual pins layer: $e');
    }
  }

  // Create empty cluster layer
  Future<void> _createEmptyClusterLayer() async {
    if (_mapController == null) return;
    debugPrint('📍 🏗️ Creating empty cluster layer...');

    try {
      // Remove existing layer/source
      await _mapController!
          .removeLayer('cluster-pins-layer')
          .catchError((_) {});
      await _mapController!
          .removeSource('cluster-pins-source')
          .catchError((_) {});

      // Add empty source
      await _mapController!.addSource(
        'cluster-pins-source',
        const GeojsonSourceProperties(
          data: {
            'type': 'FeatureCollection',
            'features': [],
          },
        ),
      );

      // Add layer
      await _mapController!.addSymbolLayer(
        'cluster-pins-source',
        'cluster-pins-layer',
        const SymbolLayerProperties(
          iconImage: ['get', 'icon'],
          iconSize: [
            'case',
            ['get', 'isPlaying'],
            1.96,
            1.4
          ],
          iconAnchor: 'bottom',
          iconAllowOverlap: true,
          iconIgnorePlacement: true,
        ),
      );

      debugPrint('📍 ✅ Created empty cluster layer');
    } catch (e) {
      debugPrint('📍 ❌ Error creating empty cluster layer: $e');
    }
  }

  // Initialize empty glow sources
  Future<void> _initializeEmptyGlowSources() async {
    if (_mapController == null) return;

    try {
      // Remove existing glow layers/sources
      await _removeGlowLayers();

      // Create empty glow sources
      final emptySources = [
        'glow-polygons-source',
        'border-polygons-source',
        'pulse-polygons-source',
        'cluster-glow-polygons-source',
        'cluster-border-polygons-source',
        'cluster-pulse-polygons-source',
      ];

      final emptyData = {
        'type': 'FeatureCollection',
        'features': [],
      };

      for (final sourceId in emptySources) {
        await _mapController!.addSource(
          sourceId,
          GeojsonSourceProperties(data: emptyData),
        );
      }

      // Add empty glow layers
      await _addGlowLayers();

      debugPrint('📍 ✅ Initialized empty glow sources');
    } catch (e) {
      debugPrint('📍 ❌ Error initializing empty glow sources: $e');
    }
  }

  // Track features in memory since MapLibre doesn't provide direct access
  // (using existing _layerFeatures defined above)

  // Get current features from a layer source
  Future<List<Map<String, dynamic>>> _getCurrentLayerFeatures(
      String sourceId) async {
    return _layerFeatures[sourceId] ?? [];
  }

  // Add features to memory tracking
  void _addToLayerFeatures(
      String sourceId, List<Map<String, dynamic>> features) {
    if (!_layerFeatures.containsKey(sourceId)) {
      _layerFeatures[sourceId] = [];
    }
    _layerFeatures[sourceId]!.addAll(features);
  }

  // Track cluster counts to detect changes
  final Map<String, int> _clusterCounts = {};

  // Check if cluster count has changed
  bool _hasClusterCountChanged(String clusterId, int newCount) {
    final oldCount = _clusterCounts[clusterId];
    if (oldCount != newCount) {
      _clusterCounts[clusterId] = newCount;
      return true;
    }
    return false;
  }

  // Remove outdated cluster features from memory tracking
  void _removeOutdatedClusters(Set<String> updatedClusterIds) {
    if (!_layerFeatures.containsKey('cluster-pins-source')) return;

    final features = _layerFeatures['cluster-pins-source']!;
    features.removeWhere((feature) {
      final properties = feature['properties'] as Map<String, dynamic>?;
      if (properties != null && properties['isCluster'] == true) {
        final clusterId = properties['id']?.toString();
        if (clusterId != null && updatedClusterIds.contains(clusterId)) {
          // This cluster is being updated, remove the old version
          return true;
        }
      }
      return false;
    });
  }

  // Clear layer features tracking
  void _clearLayerFeatures([String? sourceId]) {
    if (sourceId != null) {
      _layerFeatures[sourceId]?.clear();
      // Also clear cluster counts when clearing cluster source
      if (sourceId == 'cluster-pins-source') {
        _clusterCounts.clear();
      }
    } else {
      _layerFeatures.clear();
      _clusterCounts.clear();
    }
  }

  // Create a GeoJSON feature for a pin

  // Update cluster layer with new features - optimized to only add unrendered pins
  Future<void> _updateClusterLayerFeatures(List<Map<String, dynamic>> clusters,
      {bool forceFullUpdate = false}) async {
    if (_mapController == null) return;

    try {
      // Get current features from memory tracking
      final currentFeatures =
          await _getCurrentLayerFeatures('cluster-pins-source');
      final existingPinIds = <String>{};
      final existingClusterIds = <String>{};

      // Extract existing pin and cluster IDs to avoid duplicates
      for (final feature in currentFeatures) {
        final properties = feature['properties'] as Map<String, dynamic>?;
        if (properties != null) {
          final id = properties['id']?.toString();
          if (id != null) {
            if (properties['isCluster'] == true) {
              existingClusterIds.add(id);
            } else {
              existingPinIds.add(id);
            }
          }
        }
      }

      final newFeatures = <Map<String, dynamic>>[];
      final updatedClusterIds = <String>{};

      debugPrint(
          '📍 🎯 Updating cluster layer: ${clusters.length} items, ${existingPinIds.length} existing pins, ${existingClusterIds.length} existing clusters');

      for (final cluster in clusters) {
        final lat = cluster['latitude'] as double;
        final lng = cluster['longitude'] as double;

        if (cluster['isCluster'] == true) {
          final count = cluster['count'] as int;
          final clusterId = cluster['id'];

          // Check if this cluster needs updating (new cluster or count changed)
          final needsUpdate = forceFullUpdate ||
              !existingClusterIds.contains(clusterId) ||
              _hasClusterCountChanged(clusterId, count);

          if (needsUpdate) {
            debugPrint(
                '📍 🎯 Adding/updating cluster feature: $clusterId with $count pins');
            updatedClusterIds.add(clusterId);

            newFeatures.add({
              'type': 'Feature',
              'geometry': {
                'type': 'Point',
                'coordinates': [lng, lat],
              },
              'properties': {
                'id': clusterId,
                'count': count,
                'icon': 'cluster-icon-$count',
                'isCluster': true,
              },
            });
          }
        } else {
          // Individual pins in cluster view - only add if not already rendered
          final pinData = cluster['pin'] as Map<String, dynamic>;
          final pinId = pinData['id']?.toString();

          if (pinId != null &&
              (forceFullUpdate || !existingPinIds.contains(pinId))) {
            debugPrint(
                '📍 🎯 Adding NEW individual pin feature in cluster view: $pinId');

            // Use isolate-based pin feature computation for single pin
            final features = await PinFeatureService.instance
                .computePinFeatures(pins: [pinData]);
            if (features.isNotEmpty) {
              final feature = features.first;
              // Mark as individual pin in cluster view
              feature['properties']['isCluster'] = false;
              newFeatures.add(feature);
            }
          }
        }
      }

      if (newFeatures.isNotEmpty || forceFullUpdate) {
        if (forceFullUpdate) {
          // Clear existing features and start fresh
          // _clearLayerFeatures('cluster-pins-source');
          _addToLayerFeatures('cluster-pins-source', newFeatures);
          debugPrint(
              '📍 🎯 Full cluster layer update with ${newFeatures.length} features');
        } else {
          // Remove outdated clusters and add new features
          // _removeOutdatedClusters(updatedClusterIds);
          _addToLayerFeatures('cluster-pins-source', newFeatures);
          debugPrint(
              '📍 🎯 Incremental cluster layer update: ${newFeatures.length} new features');
        }

        // Get all features for the final update
        final allFeatures =
            await _getCurrentLayerFeatures('cluster-pins-source');

        await _mapController!.setGeoJsonSource(
          'cluster-pins-source',
          {
            'type': 'FeatureCollection',
            'features': allFeatures,
          },
        );

        debugPrint(
            '📍 ✅ Cluster layer updated with ${allFeatures.length} total features (${newFeatures.length} new)');
      } else {
        debugPrint(
            '📍 ✅ No cluster layer updates needed - all features already rendered');
      }
    } catch (e) {
      debugPrint('📍 ❌ Error updating cluster layer features: $e');
    }
  }

  // Update cluster symbols for tap handling
  void _updateClusterSymbolsFromFeatures(List<Map<String, dynamic>> clusters) {
    // Get existing cluster IDs to avoid clearing everything
    final existingClusterIds = _clusters.map((c) => c['id']).toSet();
    final newClusterIds = clusters
        .where((c) => c['isCluster'] == true)
        .map((c) => c['id'])
        .toSet();

    // Only clear and rebuild if we have significantly different clusters
    final hasSignificantChanges =
        existingClusterIds.difference(newClusterIds).isNotEmpty ||
            newClusterIds.difference(existingClusterIds).isNotEmpty;

    if (!hasSignificantChanges && _clusterSymbols.isNotEmpty) {
      debugPrint(
          '📍 🎯 Skipping cluster symbol update - no significant changes detected');
      return;
    }

    debugPrint(
        '📍 🎯 Updating cluster symbols: ${existingClusterIds.length} existing → ${newClusterIds.length} new');

    _clusterSymbols.clear();
    _clusters.clear();

    for (final cluster in clusters) {
      if (cluster['isCluster'] == true) {
        final lat = cluster['latitude'] as double;
        final lng = cluster['longitude'] as double;

        final symbol = Symbol(
          cluster['id'],
          SymbolOptions(geometry: LatLng(lat, lng)),
        );
        _clusterSymbols.add(symbol);
        _clusters.add(cluster);
      }
    }

    debugPrint(
        '📍 🎯 Updated cluster symbols: ${_clusterSymbols.length} symbols for tap handling');
  }

  // Update glow sources with new features (append to existing)
  Future<void> _updateGlowSources(
    List<Map<String, dynamic>> newGlowFeatures,
    List<Map<String, dynamic>> newBorderFeatures,
    List<Map<String, dynamic>> newPulseFeatures,
  ) async {
    if (_mapController == null) return;

    try {
      // Update individual glow sources
      await _mapController!.setGeoJsonSource(
        'glow-polygons-source',
        {
          'type': 'FeatureCollection',
          'features': newGlowFeatures,
        },
      );

      await _mapController!.setGeoJsonSource(
        'border-polygons-source',
        {
          'type': 'FeatureCollection',
          'features': newBorderFeatures,
        },
      );

      await _mapController!.setGeoJsonSource(
        'pulse-polygons-source',
        {
          'type': 'FeatureCollection',
          'features': newPulseFeatures,
        },
      );

      // Update cluster glow sources (same data)
      await _mapController!.setGeoJsonSource(
        'cluster-glow-polygons-source',
        {
          'type': 'FeatureCollection',
          'features': newGlowFeatures,
        },
      );

      await _mapController!.setGeoJsonSource(
        'cluster-border-polygons-source',
        {
          'type': 'FeatureCollection',
          'features': newBorderFeatures,
        },
      );

      await _mapController!.setGeoJsonSource(
        'cluster-pulse-polygons-source',
        {
          'type': 'FeatureCollection',
          'features': newPulseFeatures,
        },
      );
    } catch (e) {
      debugPrint('📍 ❌ Error updating glow sources: $e');
    }
  }

  // Clear existing symbols and layers
  Future<void> _clearExistingSymbols() async {
    if (_mapController == null) return;

    final pinCount = _testPinSymbols.length;
    final clusterCount = _clusterSymbols.length;

    // Clear layer-based approach
    if (_layersInitialized) {
      try {
        // Remove layers
        await _mapController!
            .removeLayer('individual-pins-layer')
            .catchError((e) => null);
        await _mapController!
            .removeLayer('cluster-pins-layer')
            .catchError((e) => null);

        // Remove sources
        await _mapController!
            .removeSource('individual-pins-source')
            .catchError((e) => null);
        await _mapController!
            .removeSource('cluster-pins-source')
            .catchError((e) => null);

        _layersInitialized = false;
        debugPrint('📍 Cleared layer-based approach');
      } catch (e) {
        debugPrint('📍 Error clearing layers: $e');
      }
    }

    // Clear individual pin symbols (fallback approach)
    // Create a copy of the list to avoid concurrent modification
    final pinSymbolsCopy = List.from(_testPinSymbols);
    for (final symbol in pinSymbolsCopy) {
      await _mapController!.removeSymbol(symbol).catchError((e) {
        debugPrint('Error removing pin symbol ${symbol.id}: $e');
      });
    }
    _testPinSymbols.clear();

    // Clear cluster symbols (fallback approach)
    // Create a copy of the list to avoid concurrent modification
    final clusterSymbolsCopy = List.from(_clusterSymbols);
    for (final symbol in clusterSymbolsCopy) {
      await _mapController!.removeSymbol(symbol).catchError((e) {
        debugPrint('Error removing cluster symbol ${symbol.id}: $e');
      });
    }
    _clusterSymbols.clear();
    _clusters.clear();

    // Clear feature tracking for progressive rendering
    _clearLayerFeatures();

    // Only clear realPinData if we're doing a complete rebuild
    // This prevents losing pin data during layer switches
    if (!_isPinFetchInProgress) {
      _realPinData.clear();
    }

    debugPrint('📍 Cleared $pinCount pins and $clusterCount clusters');
  }

  // Legacy method - now only returns cached pins (no API fetching)
  Future<List<Map<String, dynamic>>> _getCachedOrFetchPins(
      {bool preserveExisting = true}) async {
    debugPrint('📍 _getCachedOrFetchPins called - returning cached pins only');

    // Return all cached pins as a list (no API fetching)
    final allPins = _pinCache.values.toList();
    _allPinsData = allPins; // Update the existing field for compatibility

    debugPrint('📍 Returning ${allPins.length} cached pins');
    
    return allPins;
  }

  // Legacy method - now disabled (no API calls)
  Future<void> _fetchAndCachePins({bool preserveExisting = true}) async {
    debugPrint(
        '📍 _fetchAndCachePins called - now disabled (use smart fetch manager instead)');

    // This method is now disabled to prevent duplicate API calls
    // All pin fetching is handled by SmartLocationFetchManager
    return;
  }

  // Helper methods for layer feature tracking (moved to avoid duplication)

  // New method to display clusters efficiently
  Future<void> _displayClusters(List<Map<String, dynamic>> clusters) async {
    if (_mapController == null) return;

    // Get the cluster counts we actually need and add only those icons
    final neededCounts = clusters
        .where((c) => c['isCluster'] == true)
        .map((c) => c['count'] as int)
        .toSet();

    await _addNeededClusterIcons(neededCounts);

    for (final cluster in clusters) {
      if (cluster['isCluster'] == true) {
        // Display cluster symbol
        final count = cluster['count'] as int;
        final center = LatLng(cluster['latitude'], cluster['longitude']);

        final clusterSymbol = await _mapController!.addSymbol(
          SymbolOptions(
            geometry: center,
            iconImage: 'cluster-icon-$count',
            iconSize: _pinBounceAnimation.value,
            iconAnchor: 'bottom',
            draggable: false,
          ),
        );

        _clusterSymbols.add(clusterSymbol);
        _clusters.add(cluster);
      } else {
        // Display individual pin
        final pinData = cluster['pin'] as Map<String, dynamic>;
        await _addOptimizedIndividualPin(pinData);
      }
    }
  }

  // New method to display individual pins efficiently
  Future<void> _displayIndividualPins(
      List<Map<String, dynamic>> pinsData) async {
    for (final pinData in pinsData) {
      await _addOptimizedIndividualPin(pinData);
    }
  }

  // Optimized version of _addIndividualPin
  Future<void> _addOptimizedIndividualPin(Map<String, dynamic> pinData) async {
    if (_mapController == null) return;

    final pinId = pinData['id']?.toString() ?? '';

    // Determine icon image
    String iconImageId =
        'music-pin-${pinData['rarity']?.toLowerCase() ?? 'common'}';

    // Try to add skin image if available
    String? skinImageUrl;
    if (pinData['skinDetails'] != null &&
        pinData['skinDetails']['image'] != null) {
      skinImageUrl = pinData['skinDetails']['image'].toString();
    } else if (mounted) {
      final skinProvider = Provider.of<SkinProvider>(context, listen: false);
      final matched = skinProvider.availableSkins.firstWhere(
        (s) => s.id == pinData['skin'],
        orElse: () => PinSkin(
          id: -1,
          name: 'Unknown',
          image: '',
          createdAt: DateTime.now(),
          unlockRule: 'FREE',
          locked: false,
        ),
      );
      if (matched.id != -1 && matched.image.isNotEmpty) {
        skinImageUrl = matched.image;
      }
    }

    if (skinImageUrl != null && skinImageUrl.isNotEmpty) {
      final customImageId = 'pin-skin-${pinData['id']}';
      final added =
          await _addSkinImage(imageUrl: skinImageUrl, imageId: customImageId);
      if (added) {
        iconImageId = customImageId;
      }
    }

    // Determine size based on playing state
    double iconSize = _pinBounceAnimation.value;
    if (_currentlyPlayingPinId != null && _currentlyPlayingPinId == pinId) {
      iconSize *= 1.4;
    }

    final symbol = await _mapController!.addSymbol(
      SymbolOptions(
        geometry: LatLng(pinData['latitude'], pinData['longitude']),
        iconImage: iconImageId,
        iconSize: iconSize,
        iconAnchor: 'bottom',
        draggable: false,
      ),
    );

    // Store pin data with symbol ID for tap handling
    _realPinData[symbol.id] = pinData;
    _testPinSymbols.add(symbol);
  }

  // Add glow effects incrementally to existing glow layers (for progressive rendering)
  Future<void> _addIncrementalGlowEffects(
      List<Map<String, dynamic>> pinsData) async {
    if (_mapController == null || pinsData.isEmpty) return;

    try {
      final glowFeatures = <Map<String, dynamic>>[];
      final borderFeatures = <Map<String, dynamic>>[];
      final pulseFeatures = <Map<String, dynamic>>[];

      // Create glow features for this batch
      for (final pinData in pinsData) {
        final pinId = pinData['id']?.toString() ?? '';
        final lat = pinData['latitude'] as double?;
        final lng = pinData['longitude'] as double?;
        if (lat == null || lng == null) {
          debugPrint('🌟 Skipping glow for pin with null coordinates: $pinId');
          continue;
        }
        final auraRadius = (pinData['aura_radius'] as num?)?.toDouble() ?? 25.0;
        final rarity = _getPinRarity(pinData);
        final pinColor = _getPinColor(rarity);
        final glowColor =
            '#${pinColor.value.toRadixString(16).substring(2).toUpperCase()}';

        // Create glow layers (reduced from 4 to 2 for performance)
        for (int i = 1; i >= 0; i--) {
          final radiusMultiplier = 1.0 + (i * 0.3);
          final circle =
              _createCirclePolygon(lat, lng, auraRadius * radiusMultiplier);

          glowFeatures.add({
            'type': 'Feature',
            'geometry': circle,
            'properties': {
              'fill-color': glowColor,
              'fill-opacity': 0.15 - (i * 0.05),
              'pin_id': pinId,
              'color': glowColor,
              'base_radius': auraRadius,
              'latitude': lat,
              'longitude': lng,
            },
          });
        }

        // Create border feature
        final borderCircle = _createCirclePolygon(lat, lng, auraRadius * 1.1);
        borderFeatures.add({
          'type': 'Feature',
          'geometry': borderCircle,
          'properties': {
            'stroke-color': glowColor,
            'stroke-width': 2,
            'stroke-opacity': 0.4,
            'pin_id': pinId,
            'color': glowColor,
            'base_radius': auraRadius,
            'latitude': lat,
            'longitude': lng,
          },
        });

        // Create pulse feature
        final pulseCircle = _createCirclePolygon(lat, lng, auraRadius);
        pulseFeatures.add({
          'type': 'Feature',
          'geometry': pulseCircle,
          'properties': {
            'fill-color': glowColor,
            'fill-opacity': 0.3,
            'pin_id': pinId,
            'color': glowColor,
            'base_radius': auraRadius,
            'latitude': lat,
            'longitude': lng,
          },
        });
      }

      // Add to existing glow sources incrementally
      await _addToExistingGlowSources(
          glowFeatures, borderFeatures, pulseFeatures);

      debugPrint(
          '🌟 Added incremental glow effects for ${pinsData.length} pins');
    } catch (e) {
      debugPrint('🌟 Error adding incremental glow effects: $e');
    }
  }

  // Add glow features to existing glow sources incrementally
  Future<void> _addToExistingGlowSources(
    List<Map<String, dynamic>> glowFeatures,
    List<Map<String, dynamic>> borderFeatures,
    List<Map<String, dynamic>> pulseFeatures,
  ) async {
    if (_mapController == null) return;

    try {
      // Add glow features to memory and update source
      if (glowFeatures.isNotEmpty) {
        _addToGlowFeatureMemory('glow-polygons-source', glowFeatures);
        final allGlowFeatures =
            await _getCurrentGlowFeatures('glow-polygons-source');

        await _mapController!.setGeoJsonSource(
          'glow-polygons-source',
          {
            'type': 'FeatureCollection',
            'features': allGlowFeatures,
          },
        );
      }

      // Add border features to memory and update source
      if (borderFeatures.isNotEmpty) {
        _addToGlowFeatureMemory('border-polygons-source', borderFeatures);
        final allBorderFeatures =
            await _getCurrentGlowFeatures('border-polygons-source');

        await _mapController!.setGeoJsonSource(
          'border-polygons-source',
          {
            'type': 'FeatureCollection',
            'features': allBorderFeatures,
          },
        );
      }

      // Add pulse features to memory and update source
      if (pulseFeatures.isNotEmpty) {
        _addToGlowFeatureMemory('pulse-polygons-source', pulseFeatures);
        final allPulseFeatures =
            await _getCurrentGlowFeatures('pulse-polygons-source');

        await _mapController!.setGeoJsonSource(
          'pulse-polygons-source',
          {
            'type': 'FeatureCollection',
            'features': allPulseFeatures,
          },
        );
      }

      debugPrint(
          '🌟 Updated existing glow sources with ${glowFeatures.length} glow features (accumulated across batches)');
    } catch (e) {
      debugPrint('🌟 Error updating existing glow sources: $e');
    }
  }

  // Memory tracking for glow features across batches
  final Map<String, List<Map<String, dynamic>>> _glowFeatureMemory = {};

  // Get current features from a glow source (with memory tracking)
  Future<List<Map<String, dynamic>>> _getCurrentGlowFeatures(
      String sourceId) async {
    try {
      // Return accumulated glow features from memory
      return _glowFeatureMemory[sourceId] ?? [];
    } catch (e) {
      debugPrint('🌟 Error getting current glow features from $sourceId: $e');
      return [];
    }
  }

  // Add glow features to memory tracking
  void _addToGlowFeatureMemory(
      String sourceId, List<Map<String, dynamic>> features) {
    if (_glowFeatureMemory[sourceId] == null) {
      _glowFeatureMemory[sourceId] = [];
    }
    _glowFeatureMemory[sourceId]!.addAll(features);
    debugPrint(
        '🌟 Added ${features.length} features to $sourceId memory (total: ${_glowFeatureMemory[sourceId]!.length})');
  }

  // Clear glow feature memory (for layer rebuilds)
  void _clearGlowFeatureMemory() {
    _glowFeatureMemory.clear();
    debugPrint('🌟 Cleared glow feature memory');
  }

  // New optimized glow circles method (for full layer creation)
  Future<void> _addOptimizedGlowCircles(
      List<Map<String, dynamic>> pinsData) async {
    if (_mapController == null || pinsData.isEmpty) return;

    try {
      final glowFeatures = <Map<String, dynamic>>[];
      final borderFeatures = <Map<String, dynamic>>[];
      final pulseFeatures = <Map<String, dynamic>>[];

      for (final pinData in pinsData) {
        final pinId = pinData['id']?.toString() ?? '';
        final lat = pinData['latitude'] as double?;
        final lng = pinData['longitude'] as double?;
        if (lat == null || lng == null) {
          debugPrint('🌟 Skipping glow for pin with null coordinates: $pinId');
          continue;
        }
        final auraRadius = (pinData['aura_radius'] as num?)?.toDouble() ?? 25.0;
        final rarity = _getPinRarity(pinData);
        final pinColor = _getPinColor(rarity);
        final glowColor =
            '#${pinColor.value.toRadixString(16).substring(2).toUpperCase()}';

        // Create glow layers (reduced from 4 to 2 for performance)
        for (int i = 1; i >= 0; i--) {
          final radiusMultiplier = 1.0 + (i * 0.3);
          final circle =
              _createCirclePolygon(lat, lng, auraRadius * radiusMultiplier);

          glowFeatures.add({
            'type': 'Feature',
            'geometry': circle,
            'properties': {
              'id': '${pinId}_glow_$i',
              'pin_id': pinId,
              'layer_index': i,
              'color': glowColor,
              'opacity_multiplier':
                  0.06 / (i + 1), // Reduced opacity for performance
            },
          });
        }

        // Create border and pulse features
        final borderCircle = _createCirclePolygon(lat, lng, auraRadius);
        borderFeatures.add({
          'type': 'Feature',
          'geometry': borderCircle,
          'properties': {
            'id': '${pinId}_border',
            'pin_id': pinId,
            'color': glowColor,
          },
        });

        final pulseCircle = _createCirclePolygon(lat, lng, auraRadius * 1.05);
        pulseFeatures.add({
          'type': 'Feature',
          'geometry': pulseCircle,
          'properties': {
            'id': '${pinId}_pulse',
            'pin_id': pinId,
            'color': glowColor,
            'base_radius': auraRadius,
            'latitude': lat,
            'longitude': lng,
          },
        });
      }

      // Store for animation
      _glowFeatures = pulseFeatures;

      // Add sources and layers efficiently
      await _addGlowSources(glowFeatures, borderFeatures, pulseFeatures);
      await _addGlowLayers();

      debugPrint('🌟 Added optimized glow effects for ${pinsData.length} pins');
    } catch (e) {
      debugPrint('🌟 Error adding optimized glow circles: $e');
    }
  }

  // Helper method to add glow sources
  Future<void> _addGlowSources(
    List<Map<String, dynamic>> glowFeatures,
    List<Map<String, dynamic>> borderFeatures,
    List<Map<String, dynamic>> pulseFeatures,
  ) async {
    if (_mapController == null) return;

    // Remove existing sources first
    await _removeGlowLayers();

    final sources = [
      {
        'id': 'glow-polygons-source',
        'features': glowFeatures,
        'layer': 'individual-glow-layer'
      },
      {
        'id': 'border-polygons-source',
        'features': borderFeatures,
        'layer': 'individual-glow-layer'
      },
      {
        'id': 'pulse-polygons-source',
        'features': pulseFeatures,
        'layer': 'individual-glow-layer'
      },
      // Duplicate sources for cluster glow (can be same data)
      {
        'id': 'cluster-glow-polygons-source',
        'features': glowFeatures,
        'layer': 'cluster-glow-layer'
      },
      {
        'id': 'cluster-border-polygons-source',
        'features': borderFeatures,
        'layer': 'cluster-glow-layer'
      },
      {
        'id': 'cluster-pulse-polygons-source',
        'features': pulseFeatures,
        'layer': 'cluster-glow-layer'
      },
    ];

    for (final sourceConfig in sources) {
      try {
        await _mapController!.addSource(
          sourceConfig['id'] as String,
          GeojsonSourceProperties(
            data: {
              'type': 'FeatureCollection',
              'features': sourceConfig['features'],
            },
          ),
        );
      } catch (e) {
        if (e.toString().contains('already exists')) {
          await _mapController!.setGeoJsonSource(
            sourceConfig['id'] as String,
            {
              'type': 'FeatureCollection',
              'features': sourceConfig['features'],
            },
          );
        } else {
          debugPrint('Error adding source ${sourceConfig['id']}: $e');
        }
      }
    }
  }

  // Helper method to add glow layers
  Future<void> _addGlowLayers() async {
    if (_mapController == null) return;

    // Add individual glow layers below pins
    for (int i = 1; i >= 0; i--) {
      await _mapController!.addFillLayer(
        'glow-polygons-source',
        'individual-glow-fill-$i',
        const FillLayerProperties(
          fillColor: ['get', 'color'],
          fillOpacity: ['get', 'opacity_multiplier'],
          fillAntialias: true,
        ),
        filter: [
          '==',
          ['get', 'layer_index'],
          i
        ],
        belowLayerId: 'poi-labels',
      );
    }

    // Add individual border layer below pins
    await _mapController!.addLineLayer(
      'border-polygons-source',
      'individual-aura-border-layer',
      const LineLayerProperties(
        lineColor: ['get', 'color'],
        lineWidth: 1.0,
        lineOpacity: 0.2,
      ),
      minzoom: _maxZoomForClustering,
      belowLayerId: 'poi-labels',
    );

    // Add individual pulse layer below pins
    await _mapController!.addFillLayer(
      'pulse-polygons-source',
      'individual-glow-pulse-layer',
      const FillLayerProperties(
        fillColor: ['get', 'color'],
        fillOpacity: 0.08,
        fillAntialias: true,
      ),
      belowLayerId: 'poi-labels',
    );

    // Add cluster glow layers below cluster pins
    for (int i = 1; i >= 0; i--) {
      await _mapController!.addFillLayer(
        'cluster-glow-polygons-source',
        'cluster-glow-fill-$i',
        const FillLayerProperties(
          fillColor: ['get', 'color'],
          fillOpacity: ['get', 'opacity_multiplier'],
          fillAntialias: true,
        ),
        filter: [
          '==',
          ['get', 'layer_index'],
          i
        ],
        belowLayerId: 'cluster-pins-layer',
      );
    }

    // Add cluster border layer below cluster pins
    await _mapController!.addLineLayer(
      'cluster-border-polygons-source',
      'cluster-aura-border-layer',
      const LineLayerProperties(
        lineColor: ['get', 'color'],
        lineWidth: 1.0,
        lineOpacity: 0.2,
      ),
      minzoom: _maxZoomForClustering,
      belowLayerId: 'cluster-pins-layer',
    );

    // Add cluster pulse layer below cluster pins
    await _mapController!.addFillLayer(
      'cluster-pulse-polygons-source',
      'cluster-glow-pulse-layer',
      const FillLayerProperties(
        fillColor: ['get', 'color'],
        fillOpacity: 0.08,
        fillAntialias: true,
      ),
      belowLayerId: 'cluster-pins-layer',
    );

    // Create a composite individual glow layer for easy visibility control
    // This will control all individual glow sub-layers
    // (Note: MapLibre doesn't support layer groups, so we handle this in _switchLayerVisibility)
  }

  // Helper method to configure symbol settings
  Future<void> _configureSymbolSettings() async {
    if (_mapController == null) return;

    await _mapController!.setSymbolIconAllowOverlap(true);
    await _mapController!.setSymbolIconIgnorePlacement(true);
    await _mapController!.setSymbolTextAllowOverlap(false);
    await _mapController!.setSymbolTextIgnorePlacement(false);
  }

  // Helper method to start optimized animations
  void _startOptimizedAnimations() {
    _startGlowAnimation();
    _startPinBounceAnimation();

    // Restore playing pin highlight if active
    if (_currentlyPlayingPinId != null) {
      Future.delayed(const Duration(milliseconds: 200), () {
        if (mounted && _currentlyPlayingPinId != null) {
          _startPlayingPinGlowAnimation();
        }
      });
    }
  }

  // ... existing code continues ...

  // Calculate distance in meters between two geographic points
  double _calculateDistanceInMeters(
      double lat1, double lon1, double lat2, double lon2) {
    return Geolocator.distanceBetween(lat1, lon1, lat2, lon2);
  }

  // Sort pins by distance from user location (nearest first) for progressive rendering
  List<Map<String, dynamic>> _sortPinsByDistanceFromUser(
      List<Map<String, dynamic>> pins) {
    if (_currentPosition == null || pins.isEmpty) {
      debugPrint(
          '📍 🎯 No user position available or no pins to sort, returning original order');
      return pins;
    }

    final userLat = _currentPosition!.latitude;
    final userLng = _currentPosition!.longitude;

    debugPrint(
        '📍 🎯 Sorting ${pins.length} pins by distance from user at ($userLat, $userLng)');

    // Create a list of pins with their distances
    final pinsWithDistance = pins.map((pin) {
      final pinLat = pin['latitude'] as double?;
      final pinLng = pin['longitude'] as double?;

      if (pinLat == null || pinLng == null) {
        return {'pin': pin, 'distance': double.infinity};
      }

      final distance =
          _calculateDistanceInMeters(userLat, userLng, pinLat, pinLng);
      return {'pin': pin, 'distance': distance};
    }).toList();

    // Sort by distance (nearest first)
    pinsWithDistance.sort(
        (a, b) => (a['distance'] as double).compareTo(b['distance'] as double));

    // Extract the sorted pins
    final sortedPins = pinsWithDistance
        .map((item) => item['pin'] as Map<String, dynamic>)
        .toList();

    debugPrint(
        '📍 🎯 Sorted pins by distance - nearest: ${(pinsWithDistance.first['distance'] as double).toStringAsFixed(1)}m, farthest: ${(pinsWithDistance.last['distance'] as double).toStringAsFixed(1)}m');

    return sortedPins;
  }

  // Debounced timer for aura updates
  Timer? _auraUpdateTimer;

  // Update which pins the user is currently within the aura radius of (optimized with debouncing)
  void _updatePinsInAura() {
    if (_currentPosition == null) {
      _pinsInAura.clear();
      if (mounted) setState(() {});
      return;
    }

    // Debounce aura updates to avoid excessive calculations
    _auraUpdateTimer?.cancel();
    _auraUpdateTimer = Timer(const Duration(milliseconds: 500), () {
      _performAuraUpdate();
    });
  }

  // Perform the actual aura update calculation
  void _performAuraUpdate() {
    if (_currentPosition == null || !mounted) return;

    final userLat = _currentPosition!.latitude;
    final userLng = _currentPosition!.longitude;
    final Map<String, Map<String, dynamic>> pinsInRangeMap =
        {}; // Use map to prevent duplicates by ID

    // Use cached pins for better performance
    final pinsToCheck =
        _pinCache.isNotEmpty ? _pinCache.values.toList() : _allPinsData;

    // Check all available pins
    for (final pinData in pinsToCheck) {
      final pinLat = pinData['latitude'] as double?;
      final pinLng = pinData['longitude'] as double?;
      if (pinLat == null || pinLng == null) {
        // Skip pins with missing coordinates to avoid runtime errors
        continue; // move to next pin
      }

      final auraRadius = (pinData['aura_radius'] as num?)?.toDouble() ?? 25.0;

      final distance =
          _calculateDistanceInMeters(userLat, userLng, pinLat, pinLng);

      if (distance <= auraRadius) {
        final pinWithDistance = Map<String, dynamic>.from(pinData);
        pinWithDistance['current_distance'] = distance;

        // Use pin ID as key to prevent duplicates
        final pinId = pinData['id']?.toString() ?? '';
        if (pinId.isNotEmpty) {
          pinsInRangeMap[pinId] = pinWithDistance;
        }
      }
    }

    // Convert map values back to list
    final List<Map<String, dynamic>> pinsInRange =
        pinsInRangeMap.values.toList();

    // Sort by vote score (upvotes - downvotes), then by distance
    pinsInRange.sort((a, b) {
      // Get vote counts
      final aUpvotes = _getPinUpvotes(a);
      final aDownvotes = _getPinDownvotes(a);
      final bUpvotes = _getPinUpvotes(b);
      final bDownvotes = _getPinDownvotes(b);

      // Calculate net scores
      final aScore = aUpvotes - aDownvotes;
      final bScore = bUpvotes - bDownvotes;

      // Sort by score first (highest first), then by distance
      if (aScore != bScore) {
        return bScore.compareTo(aScore); // Descending order for votes
      }

      // If scores are equal, sort by distance
      final distanceA = a['current_distance'] as double;
      final distanceB = b['current_distance'] as double;
      return distanceA.compareTo(distanceB);
    });

    // Update state only if changed (optimize setState calls)
    if (pinsInRange.length != _pinsInAura.length ||
        !pinsInRange.every((pin) =>
            _pinsInAura.any((existing) => existing['id'] == pin['id']))) {
      _pinsInAura = pinsInRange;
      if (mounted) setState(() {});
      debugPrint('🎯 User is within aura of ${_pinsInAura.length} unique pins');
    }
  }

  // Create custom pin icons with glowing effects
  Future<void> _addPinIcons() async {
    if (_mapController == null) return;

    final rarities = ['legendary', 'epic', 'rare', 'uncommon', 'common'];

    for (final rarity in rarities) {
      try {
        // Use the default pin image for all rarities
        final bytes = await _createDefaultPinIcon(rarity);
        await _mapController!.addImage('music-pin-$rarity', bytes);
      } catch (e) {
        debugPrint('Error adding pin icon for $rarity: $e');
      }
    }
  }

  // Create a default pin icon using the skin collection style
  Future<Uint8List> _createDefaultPinIcon(String rarity) async {
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    final size = 180.0;

    // Draw metallic stem first
    final stemRect = Rect.fromLTWH(
        size * 0.485, // x position (centered due to thinner width)
        size * 0.55, // y position
        size * 0.03, // width (thinner - was 0.06)
        size * 0.3 // height
        );

    // Create metallic gradient for stem
    final stemGradient = LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        Colors.grey[300]!,
        Colors.grey[400]!,
        Colors.grey[600]!,
        Colors.grey[400]!,
        Colors.grey[300]!,
      ],
      stops: const [0.0, 0.25, 0.5, 0.75, 1.0],
    );

    // Draw stem with metallic effect
    canvas.drawRect(
        stemRect, Paint()..shader = stemGradient.createShader(stemRect));

    // Add stem shadow (subtle)
    final shadowPaint = Paint()
      ..color = Colors.black.withOpacity(0.12)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 2);
    canvas.drawRect(stemRect.translate(1, 1), shadowPaint);

    // Load and draw the default pin image
    final ByteData data =
        await rootBundle.load('assets/images/pins/default_pin.png');
    final Uint8List bytes = data.buffer.asUint8List();
    final codec = await ui.instantiateImageCodec(bytes);
    final frame = await codec.getNextFrame();

    // Scale and draw the image
    final rect = Rect.fromCenter(
      center: Offset(size * 0.5, size * 0.4),
      width: size * 0.4,
      height: size * 0.4,
    );

    canvas.drawImageRect(
      frame.image,
      Rect.fromLTWH(
          0, 0, frame.image.width.toDouble(), frame.image.height.toDouble()),
      rect,
      Paint(),
    );

    // Add rarity-specific effects
    if (rarity == 'legendary' || rarity == 'epic') {
      _drawSparkles(canvas, Offset(size * 0.5, size * 0.4), size * 0.25);
    }

    // Convert to image
    final picture = recorder.endRecording();
    final image = await picture.toImage(size.toInt(), size.toInt());
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    return byteData!.buffer.asUint8List();
  }

  // Draw sparkles for legendary pins
  void _drawSparkles(Canvas canvas, Offset center, double radius) {
    final sparklePaint = Paint()
      ..color = Colors.white.withOpacity(0.8)
      ..style = PaintingStyle.fill;

    final sparklePositions = [
      Offset(center.dx - radius * 0.8, center.dy - radius * 0.6),
      Offset(center.dx + radius * 0.7, center.dy - radius * 0.8),
      Offset(center.dx + radius * 0.9, center.dy + radius * 0.4),
      Offset(center.dx - radius * 0.6, center.dy + radius * 0.7),
    ];

    for (final pos in sparklePositions) {
      _drawSparkle(canvas, pos, radius * 0.1, sparklePaint);
    }
  }

  // Draw individual sparkle
  void _drawSparkle(Canvas canvas, Offset center, double size, Paint paint) {
    final path = Path()
      ..moveTo(center.dx, center.dy - size)
      ..lineTo(center.dx + size * 0.3, center.dy - size * 0.3)
      ..lineTo(center.dx + size, center.dy)
      ..lineTo(center.dx + size * 0.3, center.dy + size * 0.3)
      ..lineTo(center.dx, center.dy + size)
      ..lineTo(center.dx - size * 0.3, center.dy + size * 0.3)
      ..lineTo(center.dx - size, center.dy)
      ..lineTo(center.dx - size * 0.3, center.dy - size * 0.3)
      ..close();

    canvas.drawPath(path, paint);
  }

  // Remove existing glow layers
  Future<void> _removeGlowLayers() async {
    if (_mapController == null) return;

    try {
      final layersToRemove = [
        // Old layer names (for compatibility)
        'aura-border-layer',
        'glow-pulse-layer',
        // Individual glow layers
        'individual-aura-border-layer',
        'individual-glow-pulse-layer',
        'individual-glow-fill-0',
        'individual-glow-fill-1',
        // Cluster glow layers
        'cluster-aura-border-layer',
        'cluster-glow-pulse-layer',
        'cluster-glow-fill-0',
        'cluster-glow-fill-1',
      ];

      // Remove old glow fill layers (for compatibility)
      for (int i = 0; i <= 3; i++) {
        layersToRemove.add('glow-fill-$i');
      }

      for (final layerId in layersToRemove) {
        await _mapController!.removeLayer(layerId).catchError((e) {
          // Layer might not exist, that's okay
        });
      }

      // Remove the sources
      final sourcesToRemove = [
        // Old sources (for compatibility)
        'glow-polygons-source',
        'border-polygons-source',
        'pulse-polygons-source',
        // New individual sources
        'individual-glow-polygons-source',
        'individual-border-polygons-source',
        'individual-pulse-polygons-source',
        // New cluster sources
        'cluster-glow-polygons-source',
        'cluster-border-polygons-source',
        'cluster-pulse-polygons-source',
      ];

      for (final sourceId in sourcesToRemove) {
        await _mapController!.removeSource(sourceId).catchError((e) {
          // Source might not exist, that's okay
        });
      }
    } catch (e) {
      debugPrint('Error removing glow layers: $e');
    }
  }

  // Start pin bounce animation (optimized for performance)
  void _startPinBounceAnimation() {
    _pinBounceTimer?.cancel(); // Cancel previous timer

    // Skip animations during initial pin load
    if (_isInitialPinLoad) {
      debugPrint('📍 Skipping pin bounce animation during initial load');
      return;
    }

    // Create a subtle bouncing effect by updating pin sizes (reduced frequency for performance)
    _pinBounceTimer =
        Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (!mounted || _mapController == null || !_isMapReady) {
        timer.cancel();
        return;
      }

      try {
        final currentSize = _pinBounceAnimation.value;

        // Batch symbol updates for better performance
        final List<Future> updateTasks = [];

        // Update all individual pin symbols with bouncing size
        for (final symbol in _testPinSymbols) {
          // Check if this is the currently playing pin
          bool isPlayingPin = false;
          if (_currentlyPlayingPinId != null) {
            // Check if symbol ID matches or pin data ID matches
            if (symbol.id == _currentlyPlayingPinId) {
              isPlayingPin = true;
            } else if (_realPinData.containsKey(symbol.id)) {
              final pinData = _realPinData[symbol.id]!;
              final pinId = pinData['id']?.toString();
              if (pinId == _currentlyPlayingPinId) {
                isPlayingPin = true;
              }
            }
          }

          // Make playing pin larger and more animated
          final finalSize = isPlayingPin ? currentSize * 1.4 : currentSize;

          updateTasks.add(_mapController!
              .updateSymbol(
                  symbol,
                  SymbolOptions(
                    iconSize: finalSize,
                  ))
              .catchError((e) {
            // Symbol might have been removed, that's okay
          }));
        }

        // Update cluster symbols with bouncing size
        for (final symbol in _clusterSymbols) {
          updateTasks.add(_mapController!
              .updateSymbol(
                  symbol,
                  SymbolOptions(
                    iconSize: currentSize,
                  ))
              .catchError((e) {
            // Symbol might have been removed, that's okay
          }));
        }

        // Execute updates concurrently for better performance
        Future.wait(updateTasks).catchError((e) {
          // Silent error handling
        });
      } catch (e) {
        // Silent error handling to reduce log spam
      }
    });
  }

  // Start glow animation with a simpler, more visible approach
  void _startGlowAnimation() {
    _glowAnimationTimer?.cancel(); // Cancel previous timer

    // Skip animations during initial pin load
    if (_isInitialPinLoad) {
      debugPrint('📍 Skipping glow animation during initial load');
      return;
    }

    // Create a pulsing effect by updating the pulse polygons (reduced frequency for performance)
    _glowAnimationTimer =
        Timer.periodic(const Duration(milliseconds: 300), (timer) {
      if (!mounted || _mapController == null) {
        timer.cancel();
        return;
      }

      try {
        if (_glowFeatures.isEmpty) return;

        final animationValue = _radiusAnimation.value; // 0.0 -> 1.0

        // Create a very subtle pulsing ripple effect
        final pulsePhase = math.sin(animationValue * 2 * math.pi);
        final rippleOpacity = (0.05 + (pulsePhase.abs() * 0.08))
            .clamp(0.05, 0.13); // Reduced range
        final rippleRadius = 1.05 + (pulsePhase.abs() * 0.12); // Reduced range

        // Update pulse polygons
        final updatedPulseFeatures = <Map<String, dynamic>>[];
        for (final feature in _glowFeatures) {
          final props = feature['properties'] as Map<String, dynamic>;
          final baseRadius = props['base_radius'] as double;
          final lat = props['latitude'] as double;
          final lng = props['longitude'] as double;

          // Create new circle polygon with animated radius
          final newCircle =
              _createCirclePolygon(lat, lng, baseRadius * rippleRadius);

          updatedPulseFeatures.add({
            'type': 'Feature',
            'geometry': newCircle,
            'properties': {
              ...props,
              'pulse_opacity': rippleOpacity,
            },
          });
        }

        // Update the source with new geometries only if source exists
        try {
          _mapController!.setGeoJsonSource(
            'pulse-polygons-source',
            {
              'type': 'FeatureCollection',
              'features': updatedPulseFeatures,
            },
          ).catchError((e) {
            // Source might not exist, stop the animation to prevent spam
            if (e.toString().contains('sourceNotFound') ||
                e.toString().contains('Source not found')) {
              timer.cancel();
              return;
            }
          });
        } catch (e) {
          if (e.toString().contains('sourceNotFound') ||
              e.toString().contains('Source not found')) {
            timer.cancel();
            return;
          }
        }
      } catch (e) {
        // Silent error handling to reduce log spam
      }
    });
  }

  // Create a circle polygon with given center and radius in meters
  Map<String, dynamic> _createCirclePolygon(
      double centerLat, double centerLng, double radiusMeters) {
    final List<List<double>> coordinates = [];
    const int segments = 32; // Number of segments for circle approximation

    // Earth radius in meters
    const double earthRadius = 6371000;

    // Convert radius from meters to degrees
    final double latDelta = (radiusMeters / earthRadius) * (180 / math.pi);
    final double lngDelta = (radiusMeters / earthRadius) *
        (180 / math.pi) /
        math.cos(centerLat * math.pi / 180);

    // Generate circle points
    for (int i = 0; i <= segments; i++) {
      final double angle = (i.toDouble() / segments) * 2 * math.pi;
      final double lat = centerLat + (latDelta * math.sin(angle));
      final double lng = centerLng + (lngDelta * math.cos(angle));
      coordinates.add([lng, lat]);
    }

    return {
      'type': 'Polygon',
      'coordinates': [coordinates],
    };
  }

  /// Handler for symbol tapped events from MaplibreMapController
  void _onSymbolTapped(Symbol symbol) {
    debugPrint('🎯 ===== SYMBOL TAP DETECTED =====');
    debugPrint('🎯 Symbol tapped! ID: ${symbol.id}');
    debugPrint(
        '🎯 Available cluster symbols: ${_clusterSymbols.map((s) => s.id).toList()}');
    debugPrint('🎯 Available clusters: ${_clusters.length}');

    // First check if it's a layer-based pin by trying to find it in our cached pin data
    // Layer-based pins have their actual database IDs, not the dummy symbol IDs
    if (_realPinData.containsKey(symbol.id)) {
      debugPrint('🎯 Found layer-based pin data: ${symbol.id}');
      final pinData = _realPinData[symbol.id]!;
      debugPrint(
          '🎯 Pin details: ${pinData['title'] ?? pinData['track_title']} by ${pinData['track_artist'] ?? pinData['artist']}');
      _showPinDialog(pinData);
      return;
    }

    // Check if it's a cluster symbol
    final clusterIndex = _clusterSymbols.indexWhere((s) => s.id == symbol.id);
    if (clusterIndex != -1) {
      final cluster = _clusters[clusterIndex];
      debugPrint(
          '🎯 Cluster tapped with ${cluster['count']} pins at index $clusterIndex');
      _displayClusterPopup(cluster, symbol);
      return;
    }

    // Check if it's in our cached pin data by ID (for layer-based pins)
    var cachedPin = _pinCache[symbol.id];
    if (cachedPin != null) {
      debugPrint('🎯 Found cached pin data: ${symbol.id}');
      _showPinDialog(cachedPin);
      return;
    }

    // Additional fallback: try to resync realPinData from cache
    if (_pinCache.containsKey(symbol.id) &&
        !_realPinData.containsKey(symbol.id)) {
      _realPinData[symbol.id] = _pinCache[symbol.id]!;
      debugPrint('🎯 Resynced pin data to realPinData: ${symbol.id}');
      _showPinDialog(_pinCache[symbol.id]!);
      return;
    }

    // Fall back to test pins if not found in real pins (for symbol-based fallback)
    final idx = _testPinSymbols.indexWhere((s) => s.id == symbol.id);
    debugPrint('🎯 Found test pin at index: $idx');
    if (idx != -1) {
      final pinData = _testPinsData[idx];
      // Add coordinates for test pins
      final lat = _currentPosition!.latitude + pinData['offsetLat'];
      final lng = _currentPosition!.longitude + pinData['offsetLng'];
      final testPinWithCoords = {
        ...pinData,
        'latitude': lat,
        'longitude': lng,
      };
      testPinWithCoords['rarity'] = _getPinRarity(testPinWithCoords);
      debugPrint('🎯 Using test pin data: ${pinData['title']}');
      _showPinDialog(testPinWithCoords);
    } else {
      debugPrint('🎯 Symbol not found in any pin list');
      debugPrint(
          '🎯 Available real pin IDs: ${_realPinData.keys.take(5).toList()}...');
      debugPrint(
          '🎯 Available cached pin IDs: ${_pinCache.keys.take(5).toList()}...');
      debugPrint(
          '🎯 Available test symbol IDs: ${_testPinSymbols.map((s) => s.id).take(5).toList()}...');
      
      // Show snackbar for no pin data found
      if (mounted && !_isLoadingPins) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Pin data not found'),
            behavior: SnackBarBehavior.floating,
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  // Display cluster pin selection directly
  void _displayClusterPopup(
      Map<String, dynamic> cluster, Symbol clusterSymbol) {
    debugPrint(
        '🎯 Opening pin selection for cluster with ${cluster['count']} pins');

    final pins = List<Map<String, dynamic>>.from(cluster['pins']);

    HapticFeedback.lightImpact();

    // Show bottom sheet with pin selection directly
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => PinSelectionBottomSheetWidget(
        pins: pins,
        currentPosition: _currentPosition,
        getPinRarity: _getPinRarity,
        getPinColor: _getPinColor,
        calculateDistanceInMeters: _calculateDistanceInMeters,
        onPinTap: _showPinDialog,
      ),
    );
  }

  // Hide cluster popup (simplified since we use direct bottom sheet now)
  void _hideClusterPopup() {
    // No longer needed since we use direct bottom sheet, but keeping for compatibility
    debugPrint('🎯 Cluster popup hide called (now using direct bottom sheet)');
  }

  // Show pin dialog based on whether pin is nearby or distant
  void _showPinDialog(Map<String, dynamic> pinData) {
    // Check if pin data is valid
    if (pinData.isEmpty) {
      if (mounted && !_isLoadingPins) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Invalid pin data'),
            behavior: SnackBarBehavior.floating,
            duration: Duration(seconds: 2),
          ),
        );
      }
      return;
    }
    
    HapticFeedback.lightImpact();

    // Check if pin is in aura (nearby) - compare both string and numeric IDs
    final pinId = pinData['id'];
    final isNearby = _pinsInAura.any((pin) =>
        pin['id'] == pinId || pin['id']?.toString() == pinId?.toString());

    // If not in aura list, manually calculate distance as fallback
    bool isActuallyNearby = isNearby;
    if (!isNearby && _currentPosition != null) {
      final pinLat = pinData['latitude'] as double?;
      final pinLng = pinData['longitude'] as double?;
      final auraRadius = (pinData['aura_radius'] as num?)?.toDouble() ?? 25.0;

      if (pinLat != null && pinLng != null) {
        final distance = _calculateDistanceInMeters(
          _currentPosition!.latitude,
          _currentPosition!.longitude,
          pinLat,
          pinLng,
        );
        isActuallyNearby = distance <= auraRadius;

        debugPrint(
            '🎯 Pin $pinId manual distance check: ${distance.toStringAsFixed(1)}m (aura: ${auraRadius}m) = ${isActuallyNearby ? 'NEARBY' : 'distant'}');
      }
    }

    debugPrint(
        '🎯 Pin $pinId in aura list: $isNearby, actually nearby: $isActuallyNearby');
    debugPrint('🎯 Current _pinsInAura count: ${_pinsInAura.length}');
    debugPrint(
        '🎯 _pinsInAura IDs: ${_pinsInAura.map((p) => p['id']).toList()}');
    debugPrint(
        '🎯 Showing ${isActuallyNearby ? 'nearby' : 'distant'} pin dialog for: ${pinData['title'] ?? pinData['track_title']}');

    if (isActuallyNearby) {
      _showNearbyPinDialog(pinData);
    } else {
      _showDistantPinDialog(pinData);
    }
  }

  // Start highlighting a pin as currently playing
  void _startPlayingPinHighlight(String pinId) {
    debugPrint('🎵 Starting highlight for playing pin: $pinId');
    debugPrint('🎵 Current real pins: ${_realPinData.keys.toList()}');
    debugPrint(
        '🎵 Current test pins: ${_testPinsData.map((p) => p['id']).toList()}');

    // Stop any previous highlight
    _stopPlayingPinHighlight();

    _currentlyPlayingPinId = pinId;
    _playingPinAnimationController?.repeat(reverse: true);

    // Update pin sizes in layers
    _updatePinLayersForPlayingState();

    // Start special glow animation for playing pin
    _startPlayingPinGlowAnimation();

    // Always ensure caption is visible for the now-playing pin so it doesn't
    // disappear when playback starts.
    final playingPinData = _realPinData[pinId] ?? _pinCache[pinId];
    if (playingPinData != null) {
      final captionText = (playingPinData['caption'] as String?) ??
          (playingPinData['description'] as String?) ??
          '';
      if (captionText.trim().isNotEmpty) {
        _showCaptionPopup(pinId, captionText);
      }
    }

    debugPrint('🎵 Pin caption started: $pinId');

    // Notify parent widget about pin highlight started
    widget.onPinHighlightStarted?.call(pinId);
  }

  // Stop highlighting the currently playing pin
  void _stopPlayingPinHighlight() {
    if (_currentlyPlayingPinId == null) return;

    debugPrint(
        '🎵 Stopping highlight for playing pin: $_currentlyPlayingPinId');

    final stoppedPinId = _currentlyPlayingPinId!;
    _currentlyPlayingPinId = null;
    _playingPinAnimationController?.stop();
    _playingPinAnimationController?.reset();
    _playingPinGlowTimer?.cancel();

    // Update pin sizes in layers (remove playing state)
    _updatePinLayersForPlayingState();

    // Remove playing pin glow layers
    _removePlayingPinGlow();

    // Notify parent widget about pin highlight stopped
    widget.onPinHighlightStopped?.call(stoppedPinId);

    if (mounted) {
      setState(() {});
    }
  }

  // Start special glow animation for playing pin
  void _startPlayingPinGlowAnimation() {
    debugPrint(
        '🎵 Starting playing pin glow animation for: $_currentlyPlayingPinId');
    _playingPinGlowTimer?.cancel();

    // Skip animations during initial pin load
    if (_isInitialPinLoad) {
      debugPrint('🎵 Skipping playing pin glow animation during initial load');
      return;
    }

    // Run the first update immediately
    _updatePlayingPinGlow();

    // Reduced frequency to improve performance (was 100ms)
    _playingPinGlowTimer =
        Timer.periodic(const Duration(milliseconds: 200), (timer) {
      if (!mounted ||
          _mapController == null ||
          _currentlyPlayingPinId == null) {
        timer.cancel();
        return;
      }

      try {
        // Update playing pin glow with special animation
        _updatePlayingPinGlow();
      } catch (e) {
        debugPrint('🎵 Error updating playing pin glow: $e');
      }
    });
  }

  // Update glow effect for currently playing pin
  void _updatePlayingPinGlow() {
    if (_currentlyPlayingPinId == null || _playingPinAnimation == null) return;

    final animationValue = _playingPinAnimation!.value;

    // Create pulsing effect for playing pin - more transparent
    final pulsePhase = math.sin(animationValue * 2 * math.pi);
    final playingOpacity =
        (0.2 + (pulsePhase.abs() * 0.3)).clamp(0.2, 0.5); // More transparent
    final playingRadius =
        1.5 + (pulsePhase.abs() * 0.8); // Much larger radius change

    // Find the playing pin data (use cache first for performance)
    Map<String, dynamic>? playingPin;

    // Check cached pins first
    if (_pinCache.containsKey(_currentlyPlayingPinId)) {
      playingPin =
          Map<String, dynamic>.from(_pinCache[_currentlyPlayingPinId]!);
    } else {
      // Fallback to real pin data
      for (final entry in _realPinData.entries) {
        if (entry.key == _currentlyPlayingPinId ||
            entry.value['id']?.toString() == _currentlyPlayingPinId) {
          playingPin = Map<String, dynamic>.from(entry.value);
          playingPin['id'] = entry.key;
          break;
        }
      }

      // Check test pins if not found in real pins
      if (playingPin == null && _currentPosition != null) {
        for (final pinData in _testPinsData) {
          final testPinId = 'test_${pinData['id']?.toString() ?? ''}';
          if (testPinId == _currentlyPlayingPinId ||
              pinData['id']?.toString() == _currentlyPlayingPinId) {
            playingPin = Map<String, dynamic>.from(pinData);
            playingPin['latitude'] =
                _currentPosition!.latitude + pinData['offsetLat'];
            playingPin['longitude'] =
                _currentPosition!.longitude + pinData['offsetLng'];
            playingPin['id'] = testPinId;
            playingPin['rarity'] = _getPinRarity(playingPin);
            break;
          }
        }
      }
    }

    if (playingPin == null) return;

    final lat = playingPin['latitude'] as double;
    final lng = playingPin['longitude'] as double;

    // Increased highlight radius for better visibility with larger pin
    final highlightRadius =
        2.0; // Larger radius in meters to match the increased pin size

    // Create subtle pin highlight features - just around the pin icon
    final playingGlowFeatures = <Map<String, dynamic>>[];

    // Subtle but visible colors for pin highlight
    final colors = ['#FF1493', '#00FFFF']; // Hot pink and cyan, just 2 layers

    for (int i = 0; i < 2; i++) {
      // Larger radius for more prominent glow around the enlarged pin
      final ringRadius =
          highlightRadius * (1.2 + (i * 0.8) + (playingRadius - 1.5) * 0.3);
      final circle = _createCirclePolygon(lat, lng, ringRadius);

      playingGlowFeatures.add({
        'type': 'Feature',
        'geometry': circle,
        'properties': {
          'id': '${_currentlyPlayingPinId}_playing_glow_$i',
          'pin_id': _currentlyPlayingPinId,
          'layer_index': i,
          'color': colors[i],
          'opacity_multiplier': playingOpacity *
              (1.0 - (i * 0.9)), // More gradual fade, more transparent
        },
      });
    }

    // Update the playing pin glow source efficiently
    try {
      _mapController!.setGeoJsonSource(
        'playing-pin-glow-source',
        {
          'type': 'FeatureCollection',
          'features': playingGlowFeatures,
        },
      ).catchError((e) {
        // Source might not exist yet, create it
        _mapController!
            .addSource(
          'playing-pin-glow-source',
          GeojsonSourceProperties(
            data: {
              'type': 'FeatureCollection',
              'features': playingGlowFeatures,
            },
          ),
        )
            .then((_) {
          // Add playing pin glow layers below pin layers
          for (int i = 1; i >= 0; i--) {
            // Try to add below individual pins layer first, fallback to below cluster layer if needed
            String? belowLayer;
            if (_currentlyShowingClusterLayer) {
              belowLayer = 'cluster-pins-layer';
            } else {
              belowLayer = 'individual-pins-layer';
            }

            _mapController!
                .addFillLayer(
                  'playing-pin-glow-source',
                  'playing-pin-glow-$i',
                  const FillLayerProperties(
                    fillColor: ['get', 'color'],
                    fillOpacity: ['get', 'opacity_multiplier'],
                    fillAntialias: true,
                  ),
                  filter: [
                    '==',
                    ['get', 'layer_index'],
                    i
                  ],
                  belowLayerId: belowLayer,
                )
                .catchError((e) => debugPrint(
                    '🎵 Error adding playing pin glow layer $i: $e'));
          }
        }).catchError((e) =>
                debugPrint('🎵 Error creating playing pin glow source: $e'));
      });
    } catch (e) {
      debugPrint('🎵 Error updating playing pin glow: $e');
    }
  }

  // Remove playing pin glow layers
  void _removePlayingPinGlow() {
    if (_mapController == null) return;

    debugPrint('🎵 Removing playing pin glow layers');

    try {
      // Remove playing pin glow layers
      for (int i = 0; i < 2; i++) {
        _mapController!.removeLayer('playing-pin-glow-$i').then((_) {
          debugPrint('🎵 Removed playing pin glow layer $i');
        }).catchError((e) {
          debugPrint('🎵 Layer playing-pin-glow-$i might not exist: $e');
        });
      }

      // Remove the source
      _mapController!.removeSource('playing-pin-glow-source').then((_) {
        debugPrint('🎵 Removed playing pin glow source');
      }).catchError((e) {
        debugPrint('🎵 Playing pin glow source might not exist: $e');
      });
    } catch (e) {
      debugPrint('🎵 Error removing playing pin glow: $e');
    }
  }

  // Public methods for external control
  void startPinHighlight(String pinId) {
    _startPlayingPinHighlight(pinId);
  }

  void stopPinHighlight() {
    _stopPlayingPinHighlight();
  }

  String? get currentlyPlayingPinId => _currentlyPlayingPinId;

  /// Refresh pins with a smooth animation instead of rebuilding the entire map.
  ///
  /// Call this (e.g. after returning from the AR pin placement screen) to
  /// refresh pin data and animate new pins dropping in.
  void reloadMapStyle() {
    if (!mounted) return;

    debugPrint('🔄 Forcing map rebuild to ensure proper state.');

    // By changing the key of the MaplibreMap widget, we force it to be
    // completely rebuilt, which is a reliable way to reset its state.
    // This is necessary to fix clustering and layer issues.
    setState(() {
      _rebuildCounter++;
      _isMapReady = false;
      _mapController = null;
      _mapStyleManager = MapStyleManager(context);
      _testPinSymbols.clear();
      _clusterSymbols.clear();
      _userLocationSymbol = null;
      _layersInitialized = false;
    });

    // Re-register callbacks after map rebuild to ensure AR pins work
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _registerCallbacksAfterStyleChange();
      }
    });
  }

  // Update pin layers to reflect current playing state
  Future<void> _updatePinLayersForPlayingState() async {
    if (_mapController == null) return;

    try {
      // Get all current pin data
      final pinsToCheck =
          _pinCache.isNotEmpty ? _pinCache.values.toList() : _allPinsData;
      if (pinsToCheck.isEmpty) return;

      // Update individual pins source
      await _updateIndividualPinsSourceForPlayingState(pinsToCheck);

      // Update cluster pins source if we have clusters
      if (_clusters.isNotEmpty) {
        await _updateClusterPinsSourceForPlayingState();
      }

      debugPrint(
          '🎵 Updated pin layers for playing state: $_currentlyPlayingPinId');
    } catch (e) {
      debugPrint('🎵 Error updating pin layers for playing state: $e');
    }
  }

  // Update individual pins source with playing state
  Future<void> _updateIndividualPinsSourceForPlayingState(
      List<Map<String, dynamic>> pinData) async {
    if (_mapController == null) return;

    final features = <Map<String, dynamic>>[];

    for (final pin in pinData) {
      final pinId = pin['id']?.toString() ?? '';
      final lat = pin['latitude'] as double;
      final lng = pin['longitude'] as double;
      final rarity = _getPinRarity(pin);

      // Determine icon image (reuse existing logic)
      String iconImageId = 'music-pin-$rarity';

      // Check for custom skin
      if (pin['skinDetails'] != null && pin['skinDetails']['image'] != null) {
        final customImageId = 'pin-skin-$pinId';
        // Assume skin image was already added during initial creation
        iconImageId = customImageId;
      }

      // Determine if this pin is currently playing
      final isPlaying =
          _currentlyPlayingPinId != null && _currentlyPlayingPinId == pinId;

      features.add({
        'type': 'Feature',
        'geometry': {
          'type': 'Point',
          'coordinates': [lng, lat],
        },
        'properties': {
          'id': pinId,
          'rarity': rarity,
          'icon': iconImageId,
          'isPlaying': isPlaying,
        },
      });
    }

    // Update the source with new data
    await _mapController!.setGeoJsonSource(
      'individual-pins-source',
      {
        'type': 'FeatureCollection',
        'features': features,
      },
    ).catchError(
        (e) => debugPrint('🎵 Error updating individual pins source: $e'));
  }

  // Update cluster pins source with playing state
  Future<void> _updateClusterPinsSourceForPlayingState() async {
    if (_mapController == null || _clusters.isEmpty) return;

    final features = <Map<String, dynamic>>[];

    for (final cluster in _clusters) {
      final lat = cluster['latitude'] as double;
      final lng = cluster['longitude'] as double;

      if (cluster['isCluster'] == true) {
        final count = cluster['count'] as int;
        features.add({
          'type': 'Feature',
          'geometry': {
            'type': 'Point',
            'coordinates': [lng, lat],
          },
          'properties': {
            'id': cluster['id'],
            'count': count,
            'icon': 'cluster-icon-$count',
            'isPlaying': false, // Clusters themselves don't play
          },
        });
      } else {
        // Individual pins within cluster view
        final pinData = cluster['pin'] as Map<String, dynamic>;
        final rarity = _getPinRarity(pinData);
        final pinId = pinData['id']?.toString() ?? '';

        String iconImageId = 'music-pin-$rarity';

        // Check for custom skin
        if (pinData['skinDetails'] != null &&
            pinData['skinDetails']['image'] != null) {
          final customImageId = 'pin-skin-$pinId';
          iconImageId = customImageId;
        }

        // Determine if this pin is currently playing
        final isPlaying =
            _currentlyPlayingPinId != null && _currentlyPlayingPinId == pinId;

        features.add({
          'type': 'Feature',
          'geometry': {
            'type': 'Point',
            'coordinates': [lng, lat],
          },
          'properties': {
            'id': pinData['id'],
            'rarity': rarity,
            'icon': iconImageId,
            'isPlaying': isPlaying,
          },
        });
      }
    }

    // Update the source with new data
    await _mapController!.setGeoJsonSource(
      'cluster-pins-source',
      {
        'type': 'FeatureCollection',
        'features': features,
      },
    ).catchError(
        (e) => debugPrint('🎵 Error updating cluster pins source: $e'));
  }

  // Public getters for accessing pin data from external components
  Map<String, Map<String, dynamic>> get realPinData => _realPinData;
  List<Map<String, dynamic>> get testPinsData => _testPinsData;
  List<Map<String, dynamic>> get allPinsData => _allPinsData;
  Position? get currentPosition => _currentPosition;

  // Show bottomsheet for nearby pins (within aura) - includes play functionality
  void _showNearbyPinDialog(Map<String, dynamic> pinData) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => NearbyPinBottomSheet(
        pinData: pinData,
        onPlayPressed: () {
          if (widget.onSymbolTap != null) {
            widget.onSymbolTap!(pinData);
          }
        },
        onStartHighlight: _startPlayingPinHighlight,
        onShowCaption: _showCaptionPopup,
      ),
    );
  }

  // Show bottomsheet for distant pins - limited info, no play functionality
  void _showDistantPinDialog(Map<String, dynamic> pinData) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => DistantPinBottomSheet(
        pinData: pinData,
        currentPosition: _currentPosition,
      ),
    );
  }

  // Create a popup image for cluster pins
  Future<Uint8List> _createClusterPopupImage(
      Map<String, dynamic> cluster) async {
    final pins = List<Map<String, dynamic>>.from(cluster['pins']);
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);

    // Popup dimensions
    final popupWidth = 340.0;
    final popupHeight = 180.0;
    final cornerRadius = 20.0;
    final stemHeight = 20.0;
    final stemWidth = 30.0;

    // Total height including stem
    final totalHeight = popupHeight + stemHeight;

    // Draw drop shadow
    final shadowPaint = Paint()
      ..color = Colors.black.withOpacity(0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 12);

    final shadowPath = Path()
      ..addRRect(RRect.fromRectAndRadius(
        Rect.fromLTWH(4, 4, popupWidth, popupHeight),
        Radius.circular(cornerRadius),
      ));
    canvas.drawPath(shadowPath, shadowPaint);

    // Create glassmorphism background
    final glassGradient = LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        Colors.white.withOpacity(0.25),
        Colors.white.withOpacity(0.15),
        Colors.black.withOpacity(0.1),
        Colors.black.withOpacity(0.2),
      ],
      stops: const [0.0, 0.3, 0.7, 1.0],
    );

    final glassPaint = Paint()
      ..shader = glassGradient.createShader(
        Rect.fromLTWH(0, 0, popupWidth, popupHeight),
      );

    // Draw main popup body
    final popupPath = Path()
      ..addRRect(RRect.fromRectAndRadius(
        Rect.fromLTWH(0, 0, popupWidth, popupHeight),
        Radius.circular(cornerRadius),
      ));
    canvas.drawPath(popupPath, glassPaint);

    // Draw stem (pointing down to the cluster)
    final stemPath = Path()
      ..moveTo(popupWidth / 2 - stemWidth / 2, popupHeight)
      ..lineTo(popupWidth / 2, popupHeight + stemHeight)
      ..lineTo(popupWidth / 2 + stemWidth / 2, popupHeight)
      ..close();
    canvas.drawPath(stemPath, glassPaint);

    // Draw border with glassmorphism effect
    final borderPaint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2
      ..shader = LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Colors.white.withOpacity(0.6),
          Colors.white.withOpacity(0.2),
          Colors.white.withOpacity(0.4),
        ],
      ).createShader(Rect.fromLTWH(0, 0, popupWidth, popupHeight));

    canvas.drawPath(popupPath, borderPaint);
    canvas.drawPath(stemPath, borderPaint);

    // Add inner highlight
    final highlightPaint = Paint()
      ..shader = RadialGradient(
        center: const Alignment(-0.5, -0.5),
        radius: 1.0,
        colors: [
          Colors.white.withOpacity(0.4),
          Colors.white.withOpacity(0.1),
          Colors.transparent,
        ],
      ).createShader(Rect.fromLTWH(0, 0, popupWidth, popupHeight * 0.6));

    canvas.drawPath(
      Path()
        ..addRRect(RRect.fromRectAndRadius(
          Rect.fromLTWH(5, 5, popupWidth - 10, popupHeight * 0.5),
          Radius.circular(cornerRadius - 5),
        )),
      highlightPaint,
    );

    // Draw header
    final headerTextPainter = TextPainter(
      text: TextSpan(
        text: '${pins.length} pins nearby',
        style: const TextStyle(
          color: Colors.white,
          fontSize: 18,
          fontWeight: FontWeight.w600,
          shadows: [
            Shadow(
              color: Colors.black54,
              offset: Offset(1, 1),
              blurRadius: 2,
            ),
          ],
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    headerTextPainter.layout();
    headerTextPainter.paint(
      canvas,
      const Offset(20, 15),
    );

    // Draw close button
    final closePaint = Paint()
      ..color = Colors.white.withOpacity(0.8)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    final closeSize = 16.0;
    final closeX = popupWidth - 30;
    final closeY = 20.0;

    canvas.drawLine(
      Offset(closeX - closeSize / 2, closeY - closeSize / 2),
      Offset(closeX + closeSize / 2, closeY + closeSize / 2),
      closePaint,
    );
    canvas.drawLine(
      Offset(closeX + closeSize / 2, closeY - closeSize / 2),
      Offset(closeX - closeSize / 2, closeY + closeSize / 2),
      closePaint,
    );

    // Draw pin previews (horizontal scroll)
    final pinPreviewWidth = 80.0;
    final pinPreviewHeight = 100.0;
    final pinStartY = 50.0;
    final maxPinsToShow = math.min(pins.length, 3);

    for (int i = 0; i < maxPinsToShow; i++) {
      final pin = pins[i];
      final pinX = 20.0 + (i * (pinPreviewWidth + 10));

      // Pin preview background
      final pinBgPaint = Paint()
        ..color = _getPinColor(_getPinRarity(pin)).withOpacity(0.3);

      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(pinX, pinStartY, pinPreviewWidth, pinPreviewHeight),
          const Radius.circular(12),
        ),
        pinBgPaint,
      );

      // Pin icon
      final iconPaint = Paint()..color = Colors.white.withOpacity(0.9);

      canvas.drawCircle(
        Offset(pinX + pinPreviewWidth / 2, pinStartY + 25),
        12,
        iconPaint,
      );

      // Music note icon (simplified)
      final notePath = Path()
        ..addOval(Rect.fromCircle(
          center: Offset(pinX + pinPreviewWidth / 2 - 3, pinStartY + 25 + 3),
          radius: 3,
        ))
        ..addRect(Rect.fromLTWH(
          pinX + pinPreviewWidth / 2 + 1,
          pinStartY + 25 - 8,
          2,
          11,
        ));

      canvas.drawPath(
          notePath, Paint()..color = _getPinColor(_getPinRarity(pin)));

      // Pin title
      final titlePainter = TextPainter(
        text: TextSpan(
          text: pin['title'] ?? pin['track_title'] ?? 'Unknown',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 11, // Smaller text (was 13)
            fontWeight: FontWeight.w600, // Lighter weight
            shadows: [
              Shadow(
                color: Colors.black,
                blurRadius: 2,
                offset: Offset(0, 1),
              ),
            ],
          ),
        ),
        textDirection: TextDirection.ltr,
        maxLines: 1,
      );

      titlePainter.layout(maxWidth: pinPreviewWidth - 4);
      titlePainter.paint(
        canvas,
        Offset(pinX + 2, pinStartY + 50),
      );

      // Artist name
      final artistPainter = TextPainter(
        text: TextSpan(
          text: pin['artist'] ?? pin['track_artist'] ?? 'Unknown Artist',
          style: TextStyle(
            color: Colors.white.withOpacity(0.7),
            fontSize: 9, // Smaller text (was 11)
          ),
        ),
        textDirection: TextDirection.ltr,
        maxLines: 1,
      );

      artistPainter.layout(maxWidth: pinPreviewWidth - 4);
      artistPainter.paint(
        canvas,
        Offset(pinX + 2, pinStartY + 65),
      );
    }

    // Show "and X more" if there are more pins
    if (pins.length > maxPinsToShow) {
      final moreCount = pins.length - maxPinsToShow;
      final morePainter = TextPainter(
        text: TextSpan(
          text: '+$moreCount more',
          style: TextStyle(
            color: Colors.white.withOpacity(0.8),
            fontSize: 12, // Smaller text (was 14)
            fontStyle: FontStyle.italic,
          ),
        ),
        textDirection: TextDirection.ltr,
      );

      morePainter.layout();
      morePainter.paint(
        canvas,
        Offset(popupWidth - morePainter.width - 20, pinStartY + 80),
      );
    }

    // Convert to image
    final picture = recorder.endRecording();
    final image =
        await picture.toImage(popupWidth.toInt(), totalHeight.toInt());
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    return byteData!.buffer.asUint8List();
  }

  // Get current style URL or JSON string based on theme and selected style
  String _getCurrentStyleUrl(bool isDarkMode) {
    final styleConfig = _mapStyles[_currentMapStyle];
    if (styleConfig == null) {
      return _mapStyles[MapStyle.standard]!.lightUrl;
    }
    return isDarkMode ? styleConfig.darkUrl : styleConfig.lightUrl;
  }

  // Load saved map style from shared preferences
  Future<void> _loadSavedMapStyle() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedStyle = prefs.getString('selected_map_style') ?? 'minimal';
      final mapStyle = MapStyle.values.firstWhere(
        (style) => style.name == savedStyle,
        orElse: () => MapStyle.minimal,
      );

      if (mapStyle != _currentMapStyle) {
        setState(() {
          _currentMapStyle = mapStyle;
        });
        // Update visual effects manager with the loaded style
        _visualEffectsManager.changeMapStyle(mapStyle);
      }

      debugPrint('🎨 Loaded saved map style: $mapStyle');
    } catch (e) {
      debugPrint('⚠️ Failed to load saved map style: $e');
    }
  }

  // Load saved effects state from shared preferences
  Future<void> _loadSavedEffectsState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final weatherEnabled = prefs.getBool('weather_effects_enabled') ?? false;

      // Restore weather effects state after initialization
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted &&
            weatherEnabled &&
            !_weatherEffectsManager.weatherEnabled) {
          _weatherEffectsManager.toggleWeatherEffects();
          debugPrint('🌧️ Restored weather effects state: enabled');
        }
      });

      debugPrint('🎭 Loaded saved effects state - weather: $weatherEnabled');
    } catch (e) {
      debugPrint('⚠️ Failed to load saved effects state: $e');
    }
  }

  // Save effects state to shared preferences
  Future<void> _saveEffectsState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(
          'weather_effects_enabled', _weatherEffectsManager.weatherEnabled);
      debugPrint(
          '💾 Saved effects state - weather: ${_weatherEffectsManager.weatherEnabled}');
    } catch (e) {
      debugPrint('⚠️ Failed to save effects state: $e');
    }
  }

  // Save map style to shared preferences
  Future<void> _saveMapStyle(MapStyle style) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('selected_map_style', style.name);
      debugPrint('💾 Saved map style: $style');
    } catch (e) {
      debugPrint('⚠️ Failed to save map style: $e');
    }
  }

  // Handle theme change without screen rebuild (preserving pin layers)
  Future<void> _handleThemeChange() async {
    debugPrint('🎨 Handling theme change (preserving pin layers)');

    // CRITICAL: Set flag to preserve pin layers during theme change
    _isStyleChangeInProgress = true;
    debugPrint('🎨 🔒 Theme change in progress - pin layers will be preserved');

    // Provide haptic feedback
    HapticFeedback.lightImpact();

    // Simply trigger a rebuild with setState to apply the new theme
    setState(() {
      // Increment rebuild counter to force MaplibreMap widget rebuild with new theme
      _rebuildCounter++;
    });

    debugPrint('🎨 ✅ Theme change applied successfully without screen rebuild');
  }

  // Change map style without screen rebuild (preserving pin layers)
  Future<void> _changeMapStyle(MapStyle newStyle) async {
    if (newStyle == _currentMapStyle) return;

    debugPrint(
        '🎨 Changing map style from $_currentMapStyle to $newStyle (preserving pin layers)');

    // CRITICAL: Set flag to preserve pin layers during style change
    _isStyleChangeInProgress = true;
    debugPrint('🎨 🔒 Style change in progress - pin layers will be preserved');

    // Save the new style to shared preferences
    await _saveMapStyle(newStyle);

    // Provide haptic feedback
    HapticFeedback.lightImpact();

    // Update the map style manager
    _mapStyleManager.changeMapStyle(newStyle);

    // Update visual effects manager
    _visualEffectsManager.changeMapStyle(newStyle);

    // Update the current style and trigger rebuild with setState
    setState(() {
      _currentMapStyle = newStyle;
      // Increment rebuild counter to force MaplibreMap widget rebuild with new style
      _rebuildCounter++;
    });

    debugPrint('🎨 ✅ Map style changed successfully without screen rebuild');
  }

  // Build weather control button
  Widget _buildWeatherControlButton(bool isDarkMode) {
    return Container(
      width: 44,
      height: 44,
      decoration: BoxDecoration(
        color: isDarkMode
            ? Colors.black.withOpacity(0.8)
            : Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(25),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: GestureDetector(
        onTap: _toggleWeatherEffects,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(25),
            gradient: _weatherEffectsManager.weatherEnabled
                ? RadialGradient(
                    colors: [
                      Colors.cyan.withOpacity(0.3 +
                          _weatherEffectsManager.weatherAnimationValue * 0.2),
                      Colors.transparent,
                    ],
                  )
                : null,
          ),
          child: Icon(
            _weatherEffectsManager.currentWeather == 'snow'
                ? Icons.ac_unit
                : _weatherEffectsManager.currentWeather == 'fog'
                    ? Icons.foggy
                    : _weatherEffectsManager.currentWeather == 'clouds'
                        ? Icons.cloud
                        : _weatherEffectsManager.currentWeather == 'clear'
                            ? Icons.wb_sunny
                            : Icons.water_drop,
            color: _weatherEffectsManager.weatherEnabled
                ? Colors.cyan
                : (isDarkMode ? Colors.white : Colors.black87),
            size: 20,
          ),
        ),
      ),
    );
  }

  void _toggleWeatherEffects() {
    _weatherEffectsManager.toggleWeatherEffects();
    // Save the new effects state to SharedPreferences
    _saveEffectsState();
  }

  // Show pins in aura bottom sheet
  void _showPinsInAuraBottomSheet() {
    debugPrint('🎯 Pins in aura indicator tapped! Showing bottom sheet...');
    debugPrint('🎯 Pins in aura count: ${_pinsInAura.length}');

    HapticFeedback.lightImpact();

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => PinsInAuraBottomSheetWidget(
        pinsInAura: _pinsInAura,
        getPinRarity: _getPinRarity,
        getPinColor: _getPinColor,
        getPinUpvotes: _getPinUpvotes,
        getPinDownvotes: _getPinDownvotes,
        onPinTap: _showPinDialog,
      ),
    ).then((_) {
      debugPrint('🎯 Bottom sheet closed');
    }).catchError((error) {
      debugPrint('🎯 Error showing bottom sheet: $error');
    });
  }

  // Restore pin state after map style change
  Future<void> _restorePinStateAfterStyleChange(
    Set<String> renderedPins,
    Map<String, Map<String, dynamic>> pinCache,
    Map<String, Map<String, dynamic>> realPinData,
    double zoom,
    PinFilterType filter,
  ) async {
    debugPrint('🎨 Restoring pin state after style change');
    debugPrint('🎨 Rendered pins: ${renderedPins.length}');
    debugPrint('🎨 Cache size: ${pinCache.length}');
    debugPrint('🎨 Zoom: $zoom, Filter: $filter');

    // Check if progressive rendering is already handling the restoration
    if (_isProgressiveRenderingActive || _isPinFetchInProgress || _progressiveUpdateTimer?.isActive == true) {
      debugPrint(
          '🎨 ⚠️ Progressive rendering already active, skipping restoration to avoid conflicts');
      return;
    }

    // Wait for map to be ready
    while (_mapController == null || !_isMapReady) {
      await Future.delayed(const Duration(milliseconds: 100));
    }

    try {
      // Restore pin data (but don't clear rendered pins if progressive rendering might be using them)
      _pinCache.clear();
      _pinCache.addAll(pinCache);
      _realPinData.clear();
      _realPinData.addAll(realPinData);

      // Only clear rendered pins if we're sure progressive rendering isn't active
      if (!_isPinFetchInProgress) {
        _renderedPinIds.clear();
        _renderedPinIds.addAll(renderedPins);
      }

      // Update current state
      _currentZoom = zoom;
      _currentFilter = filter;

      // Only reinitialize layers if progressive rendering isn't handling it
      if (!_isPinFetchInProgress &&
          !(_progressiveUpdateTimer?.isActive == true)) {
        debugPrint('🎨 Reinitializing layers for restoration');
        await _initializeEmptyLayers();

        // Restore all pins to layers
        if (renderedPins.isNotEmpty) {
          final allPins = <Map<String, dynamic>>[];
          for (final pinId in renderedPins) {
            final pin = _pinCache[pinId] ?? _realPinData[pinId];
            if (pin != null) {
              allPins.add(pin);
            }
          }

          if (allPins.isNotEmpty) {
            // Determine if we should cluster based on current zoom
            final shouldCluster = _currentZoom < _maxZoomForClustering;

            if (shouldCluster) {
              // Restore cluster layer
              final clusters = await ClusterService.instance.computeClusters(
                pins: _allPinsData,
                clusterRadius: 250.0,
                minClusterSize: 3,
              );
              await _updateClusterLayerFeatures(clusters,
                  forceFullUpdate: true);
              _updateClusterSymbolsFromFeatures(clusters);

              // Use accumulative method instead of clearing layer
              await _addPinBatchToIndividualLayer(allPins);
            } else {
              // Use accumulative method instead of clearing layer
              await _addPinBatchToIndividualLayer(allPins);
            }
          }
        }
      } else {
        debugPrint(
            '🎨 Skipping layer reinitialization - progressive rendering is active');
      }
    } catch (e) {
      debugPrint('🎨 ❌ Error restoring pin state: $e');
    }
  }

  // Build progressive rendering debug info
  Widget _buildProgressiveRenderingDebugInfo(bool isDarkMode) {
    final status = getProgressiveRenderingStatus();

    return Positioned(
      top: widget.showBottomNav ? 120 : 150, // Below pins in aura indicator
      right: 16,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: isDarkMode
              ? Colors.black.withOpacity(0.8)
              : Colors.white.withOpacity(0.9),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Colors.orange.withOpacity(0.5),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Progressive Rendering',
              style: TextStyle(
                color: isDarkMode ? Colors.white : Colors.black87,
                fontSize: 10,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Enabled: ${status['enabled']}',
              style: TextStyle(
                color: status['enabled'] ? Colors.green : Colors.red,
                fontSize: 9,
              ),
            ),
            Text(
              'Active: ${status['isActive']}',
              style: TextStyle(
                color: status['isActive']
                    ? Colors.green
                    : (isDarkMode ? Colors.white70 : Colors.black54),
                fontSize: 9,
              ),
            ),
            if (status['totalPins'] > 0)
              Text(
                'Total: ${status['totalPins']}',
                style: const TextStyle(
                  color: Colors.blue,
                  fontSize: 9,
                ),
              ),
            if (status['pendingPins'] > 0)
              Text(
                'Pending: ${status['pendingPins']}',
                style: const TextStyle(
                  color: Colors.orange,
                  fontSize: 9,
                ),
              ),
            Text(
              'Rendered: ${status['renderedPins']}',
              style: TextStyle(
                color: isDarkMode ? Colors.white70 : Colors.black54,
                fontSize: 9,
              ),
            ),
            if (status['isPinFetchInProgress'])
              const Text(
                'Fetching...',
                style: TextStyle(
                  color: Colors.cyan,
                  fontSize: 9,
                ),
              ),
          ],
        ),
      ),
    );
  }

  // RouteAware implementation for detecting navigation events

  @override
  void didPush() {
    // Route was pushed onto navigator and is now topmost route
    _isCurrentlyOnScreen = true;
    _visualEffectsManager.setScreenVisibility(true);
    debugPrint('Map Screen: didPush - screen is now visible');
  }

  @override
  void didPopNext() {
    // Route was popped and this screen is now visible
    _isCurrentlyOnScreen = true;
    _visualEffectsManager.setScreenVisibility(true);
    _resumeAnimations();
    debugPrint(
        'Map Screen: didPopNext - returned to screen, resuming animations');
  }

  @override
  void didPushNext() {
    // Another route has been pushed and this screen is no longer visible
    _isCurrentlyOnScreen = false;
    _visualEffectsManager.setScreenVisibility(false);
    _pauseAnimations();
    debugPrint('Map Screen: didPushNext - screen covered, pausing animations');
  }

  @override
  void didPop() {
    // This route has been popped off the navigator
    _isCurrentlyOnScreen = false;
    _visualEffectsManager.setScreenVisibility(false);
    debugPrint('Map Screen: didPop - screen popped, animations paused');
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isDarkMode = themeProvider.isDarkMode;

    // Check for theme changes and trigger map style rebuild if needed
    if (_previousThemeIsDarkMode != null && _previousThemeIsDarkMode != isDarkMode) {
      debugPrint('🎨 Theme change detected: ${_previousThemeIsDarkMode} → $isDarkMode');
      debugPrint('🎨 Triggering map style rebuild for theme change...');

      // Use post frame callback to avoid calling setState during build
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _handleThemeChange();
        }
      });
    }

    // Update the previous theme state
    _previousThemeIsDarkMode = isDarkMode;

    // Update visual effects managers with current screen size
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final size = MediaQuery.of(context).size;
        _visualEffectsManager.updateWithScreenSize(size);
        _weatherEffectsManager.updateWithScreenSize(size);
      }
    });

    // Create the main content widget
    Widget stackContent = Stack(
      children: [
        // Map (no onMapClick since we'll handle it with GestureDetector)
        MaplibreMap(
          key: ValueKey('map_${_currentMapStyle.name}_$_rebuildCounter'),
          styleString: _getCurrentStyleUrl(isDarkMode),
          onMapCreated: _onMapCreated,
          onStyleLoadedCallback: _onStyleLoaded,
          initialCameraPosition: CameraPosition(
            target: _currentPosition != null
                ? LatLng(
                    _currentPosition!.latitude, _currentPosition!.longitude)
                : const LatLng(AppConstants.defaultLatitude,
                    AppConstants.defaultLongitude),
            zoom: _defaultZoom,
            tilt: _defaultTilt,
            bearing: _defaultBearing,
          ),
          scrollGesturesEnabled: true,
          zoomGesturesEnabled: true,
          rotateGesturesEnabled: true,
          tiltGesturesEnabled: true,
          compassEnabled: true,
          // Position the built-in compass underneath the style toggle on the left column
          compassViewPosition: CompassViewPosition.topLeft,
          compassViewMargins: math.Point<double>(
            32, // 8 px further right than before
            (widget.showBottomNav ? 40 : 70) +
                (44 * 3) +
                (8 * 3) -
                16, // 6 px higher than before
          ),
          myLocationEnabled: false, // We use custom marker
          trackCameraPosition: true,
          attributionButtonMargins:
              const math.Point(-100, -100), // Hide attribution
        ),

        // Lightweight touch tracking overlay for pinch conflict prevention
        Positioned.fill(
          child: Listener(
            onPointerDown: (event) {
              // Track touch points for gesture conflict detection
              _gestureManager
                  .updateTouchPoints(_gestureManager.activeTouchPoints + 1);
            },
            onPointerUp: (event) {
              // Update touch points when finger is lifted
              final newCount =
                  math.max(0, _gestureManager.activeTouchPoints - 1);
              _gestureManager.updateTouchPoints(newCount);
            },
            onPointerCancel: (event) {
              // Handle cancelled touches
              final newCount =
                  math.max(0, _gestureManager.activeTouchPoints - 1);
              _gestureManager.updateTouchPoints(newCount);
            },
            behavior: HitTestBehavior
                .translucent, // Allow all gestures to pass through
          ),
        ),

        // Separate tap detector for pin clicks only
        Positioned.fill(
          child: GestureDetector(
            onTapDown: (details) async {
              debugPrint('🗺️ ===== GESTURE DETECTOR TAP =====');
              debugPrint(
                  '🗺️ Tap at: ${details.localPosition.dx}, ${details.localPosition.dy}');

              // Check with gesture manager if tap should be allowed
              if (!_gestureManager.shouldAllowTap) {
                debugPrint('🎯 Tap blocked by gesture manager');
                return;
              }

              if (_mapController != null) {
                try {
                  final screenPoint = math.Point<double>(
                      details.localPosition.dx, details.localPosition.dy);

                  // Use queryRenderedFeatures to detect actual symbol/badge taps
                  final features = await _mapController!.queryRenderedFeatures(
                    screenPoint,
                    ['individual-pins-layer', 'cluster-pins-layer'],
                    null,
                  );

                  debugPrint(
                      '🎯 Found ${features.length} features at tap point');

                  if (features.isNotEmpty) {
                    final feature = features.first as Map<String, dynamic>;
                    final properties =
                        feature['properties'] as Map<String, dynamic>?;

                    if (properties != null) {
                      final id = properties['id']?.toString();
                      final isCluster = properties['count'] != null;

                      debugPrint(
                          '🎯 Feature tapped - ID: $id, isCluster: $isCluster');

                      if (id != null) {
                        // Use immediate validation for better responsiveness
                        if (_gestureManager.canTapImmediately()) {
                          if (isCluster) {
                            // Handle cluster tap
                            final cluster = _clusters.firstWhere(
                              (c) => c['id'] == id,
                              orElse: () => <String, dynamic>{},
                            );
                            if (cluster.isNotEmpty) {
                              debugPrint(
                                  '🎯 Cluster tapped: $id with ${cluster['count']} pins');
                              _displayClusterPopup(
                                  cluster,
                                  Symbol(
                                      id,
                                      SymbolOptions(
                                        geometry: LatLng(
                                            feature['geometry']['coordinates']
                                                [1],
                                            feature['geometry']['coordinates']
                                                [0]),
                                      )));
                            }
                          } else {
                            // Handle individual pin tap
                            var pinData = _realPinData[id];
                            if (pinData == null && _pinCache.containsKey(id)) {
                              // Fallback to pin cache if not in realPinData
                              pinData = _pinCache[id];
                              debugPrint('🎯 Found pin in cache fallback: $id');
                            }
                            if (pinData != null) {
                              debugPrint('🎯 Individual pin tapped: $id');
                              _showPinDialog(pinData);
                            } else {
                              debugPrint('🎯 ❌ Pin data not found for ID: $id');
                              debugPrint(
                                  '🎯 Available realPinData IDs: ${_realPinData.keys.take(5).toList()}');
                              debugPrint(
                                  '🎯 Available pinCache IDs: ${_pinCache.keys.take(5).toList()}');
                            }
                          }
                        }
                      }
                    }
                  } else {
                    debugPrint('🗺️ No features found - empty map area tapped');
                  }
                } catch (e) {
                  debugPrint('🗺️ Error handling tap: $e');
                }
              }
            },

            behavior: HitTestBehavior
                .translucent, // Allow gestures to pass through to map
          ),
        ),

        // Loading indicator
        if (!_isMapReady)
          Container(
            color: isDarkMode ? Colors.black87 : Colors.white70,
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          ),

        // Weather effects and style-specific glitch overlays
        if (_visualEffectsManager.hasActiveEffects ||
            _weatherEffectsManager.hasActiveEffects)
          Positioned.fill(
            child: IgnorePointer(
              child: CustomPaint(
                painter: WeatherEffectsPainter(
                  // Use real weather effects from weather manager
                  particles: _weatherEffectsManager.weatherParticles,
                  lightningBolts: _weatherEffectsManager.lightningBolts,
                  lightningFlashActive:
                      _weatherEffectsManager.lightningFlashActive,
                  lightningFlashIntensity:
                      _weatherEffectsManager.lightningFlashIntensity,
                  animationValue: _weatherEffectsManager.weatherAnimationValue,
                  // Keep style effects from visual effects manager
                  matrixCharacters: _visualEffectsManager.matrixCharacters,
                  matrixGlitchActive: _visualEffectsManager.matrixGlitchActive,
                  matrixGlitchIntensity:
                      _visualEffectsManager.matrixGlitchIntensity,
                  cyberpunkGlitches: _visualEffectsManager.cyberpunkGlitches,
                  cyberpunkGlitchActive:
                      _visualEffectsManager.cyberpunkGlitchActive,
                  cyberpunkGlitchIntensity:
                      _visualEffectsManager.cyberpunkGlitchIntensity,
                  retroVHSEffects: _visualEffectsManager.retroVHSEffects,
                  retroGlitchActive: _visualEffectsManager.retroGlitchActive,
                  retroGlitchIntensity:
                      _visualEffectsManager.retroGlitchIntensity,
                  screenSize: MediaQuery.of(context).size,
                  // TV switching sequence parameters
                  isTVSwitching: _visualEffectsManager.isTVSwitching,
                  tvSwitchingPhase: _visualEffectsManager.tvSwitchingPhase,
                ),
              ),
            ),
          ),

        // Caption popups overlay
        if (_isMapReady) ..._buildCaptionPopups(),

        // Pins in aura indicator (center top, only if pins exist)
        if (_isMapReady && _pinsInAura.isNotEmpty)
          Positioned(
            top: widget.showBottomNav ? 40 : 70,
            left: 80,
            right: 80,
            child: Center(
              child: PinsInAuraIndicatorWidget(
                pinsInAura: _pinsInAura,
                pulseAnimation: _pulseAnimation,
                onTap: _showPinsInAuraBottomSheet,
                isDarkMode: isDarkMode,
              ),
            ),
          ),

        // Progressive rendering status (debug mode only)
        // if (_isMapReady && kDebugMode && _isProgressiveRenderingEnabled)
        //   _buildProgressiveRenderingDebugInfo(isDarkMode),

        // Left side control column (Map style, Settings, Weather)
        if (_isMapReady)
          Positioned(
            left: 16,
            top: widget.showBottomNav ? 40 : 70,
            child: Column(
              children: [
                // Settings button
                _buildMapControlButton(
                  context,
                  icon: Icons.settings,
                  tooltip: 'Settings',
                  onPressed: widget.onSettingsPressed ?? () {},
                  backgroundColor: themeProvider.primaryColor,
                  foregroundColor: Colors.white,
                ),
                const SizedBox(height: 8),
                // Weather control button
                _buildWeatherControlButton(isDarkMode),
                const SizedBox(height: 8),
                // Map style toggle
                MapStyleToggleWidget(
                  currentMapStyle: _currentMapStyle,
                  mapStyles: _mapStyles,
                  onStyleSelected: _changeMapStyle,
                  isDarkMode: isDarkMode,
                ),
                const SizedBox(height: 8),
                // Filter button
                _buildFilterButton(isDarkMode),
              ],
            ),
          ),

        // Right side map controls (Search, Zoom, 3D, Location, Add Pin)
        if (_isMapReady)
          Positioned(
            right: 16,
            top: widget.showBottomNav ? 40 : 70,
            child: Column(children: [
              // Search button
              _buildMapControlButton(
                context,
                icon: Icons.search,
                tooltip: 'Search',
                onPressed: widget.onSearchPressed ?? () {},
                backgroundColor: themeProvider.primaryColor,
                foregroundColor: Colors.white,
              ),
              const SizedBox(height: 8),
              // Zoom in button
              // _buildMapControlButton(
              //   context,
              //   icon: Icons.add,
              //   tooltip: 'Zoom In',
              //   onPressed: widget.onZoomIn ?? () {},
              //   backgroundColor: themeProvider.primaryColor,
              //   foregroundColor: Colors.white,
              // ),
              // const SizedBox(height: 8),
              // // Zoom out button
              // _buildMapControlButton(
              //   context,
              //   icon: Icons.remove,
              //   tooltip: 'Zoom Out',
              //   onPressed: widget.onZoomOut ?? () {},
              //   backgroundColor: themeProvider.primaryColor,
              //   foregroundColor: Colors.white,
              // ),
              // const SizedBox(height: 8),
              // 3D tilt toggle button
              _buildMapControlButton(
                context,
                icon: Icons.layers,
                tooltip: 'Toggle 3D View',
                onPressed: widget.onAdjustTilt ?? () {},
                backgroundColor: themeProvider.primaryColor,
                foregroundColor: Colors.white,
              ),
              const SizedBox(height: 8),
              // Location button with flashing animation when active

              widget.isFollowingUser == true
                  ? _buildMapControlButton(
                      context,
                      icon: Icons.my_location,
                      tooltip: 'My Location',
                      onPressed: widget.onToggleFollowUser ?? () {},
                      backgroundColor: themeProvider.primaryColor,
                      foregroundColor:
                          Colors.white.withOpacity(_gpsFlashingAnimation.value),
                    )
                  : _buildMapControlButton(
                      context,
                      icon: Icons.location_searching,
                      tooltip: 'My Location',
                      onPressed: widget.onToggleFollowUser ?? () {},
                      backgroundColor: isDarkMode
                          ? Colors.black.withOpacity(0.8)
                          : Colors.white.withOpacity(0.9),
                      foregroundColor:
                          isDarkMode ? Colors.white : Colors.black87,
                    ),
              const SizedBox(height: 8),
              // Add Pin button
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(25),
                  gradient: RadialGradient(
                    center: const Alignment(-0.3, -0.3),
                    radius: 1.2,
                    colors: [
                      const Color(0xFFE8B4FF)
                          .withOpacity(0.8), // Purple-pink from bubble
                      const Color(0xFF9BFFFF)
                          .withOpacity(0.6), // Cyan from bubble
                      const Color(0xFFFFB4E8)
                          .withOpacity(0.4), // Pink from bubble
                      Colors.white.withOpacity(0.2),
                    ],
                    stops: const [0.0, 0.3, 0.7, 1.0],
                  ),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 1.5,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFFE8B4FF).withOpacity(0.3),
                      blurRadius: 15,
                      offset: const Offset(0, 4),
                    ),
                    BoxShadow(
                      color: Colors.white.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: widget.onAddPinPressed ?? () {},
                    borderRadius: BorderRadius.circular(25),
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(25),
                        gradient: RadialGradient(
                          center: const Alignment(0.4, 0.4),
                          radius: 0.6,
                          colors: [
                            Colors.white.withOpacity(0.4),
                            Colors.white.withOpacity(0.1),
                            Colors.transparent,
                          ],
                        ),
                      ),
                      child: const Icon(
                        Icons.add_location_alt,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ),
                ),
              ),
            ]),
          ),
      ],
    );

    return Scaffold(
      extendBodyBehindAppBar: true,
      body: stackContent,
      bottomNavigationBar: widget.showBottomNav
          ? MusicPinBottomNavBar.auto(
              context: context,
              onTabSelected: (index) {
                NavigationHelper.navigateToTab(context, index);
              },
              onAddPinPressed: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Add pin feature coming soon')),
                );
              },
            )
          : null,
    );
  }

  void _onMapClick(math.Point<double> point, LatLng latLng) {
    debugPrint('🗺️ ===== MAP CLICK DETECTED =====');
    debugPrint(
        '🗺️ Map clicked at: ${point.x}, ${point.y} (${latLng.latitude}, ${latLng.longitude})');
    debugPrint('🗺️ Current pins count: ${_testPinSymbols.length}');
    debugPrint(
        '🗺️ Current position: ${_currentPosition?.latitude}, ${_currentPosition?.longitude}');
    debugPrint('🗺️ Layer-based approach initialized: $_layersInitialized');
    debugPrint(
        '🗺️ Currently showing cluster layer: $_currentlyShowingClusterLayer');

    // Use the async symbol tap handler for both layer-based and fallback detection
    _handleSymbolTap(point);
  }

  Future<void> _handleSymbolTap(math.Point<double> point) async {
    debugPrint(
        '🔍 Symbol tap detection started at point: ${point.x}, ${point.y}');
    if (_mapController == null) return;

    try {
      // Check if the tap was on a layer-based symbol
      debugPrint(
          '🔍 Querying features from layers: individual-pins-layer, cluster-pins-layer');
      debugPrint(
          '🔍 Layers initialized: $_layersInitialized, showing clusters: $_currentlyShowingClusterLayer');

      final features = await _mapController!.queryRenderedFeatures(
        point,
        ['individual-pins-layer', 'cluster-pins-layer'],
        null,
      );

      debugPrint('🔍 Found ${features.length} features');

      if (features.isNotEmpty) {
        final feature = features.first as Map<String, dynamic>;
        debugPrint('🔍 First feature: $feature');
        final properties = feature['properties'] as Map<String, dynamic>?;
        debugPrint('🔍 Feature properties: $properties');

        if (properties != null) {
          final id = properties['id']?.toString();
          final isCluster = properties['count'] != null;

          debugPrint('🔍 Feature ID: $id, isCluster: $isCluster');

          if (id != null) {
            if (isCluster) {
              // Handle cluster tap
              final cluster =
                  _clusters.firstWhere((c) => c['id'] == id, orElse: () => {});
              if (cluster.isNotEmpty) {
                debugPrint(
                    '🎯 Cluster tapped: $id with ${cluster['count']} pins');
                _displayClusterPopup(
                    cluster, Symbol(id, const SymbolOptions(geometry: LatLng(0, 0))));
              } else {
                debugPrint('🔍 Cluster not found in _clusters list');
              }
            } else {
              // Handle individual pin tap
              final pinData = _realPinData[id];
              if (pinData != null) {
                debugPrint(
                    '🎯 Pin tapped: $id - ${pinData['title'] ?? pinData['track_title']}');
                _showPinDialog(pinData);
              } else {
                debugPrint('🔍 Pin data not found in _realPinData for id: $id');
                debugPrint(
                    '🔍 Available pin IDs: ${_realPinData.keys.toList()}');
              }
            }
            return;
          }
        }
      } else {
        debugPrint('🔍 No features found in pin/cluster layers');

        // Try querying ALL features to see what's available
        try {
          final allFeatures =
              await _mapController!.queryRenderedFeatures(point, [], null);
          debugPrint('🔍 Total features at tap point: ${allFeatures.length}');
          if (allFeatures.isNotEmpty) {
            for (int i = 0; i < math.min(3, allFeatures.length); i++) {
              final feature = allFeatures[i] as Map<String, dynamic>;
              final sourceLayer = feature['sourceLayer'] ?? 'unknown';
              final properties = feature['properties'] as Map<String, dynamic>?;
              final id = properties?['id'];
              debugPrint('🔍 Feature $i: sourceLayer=$sourceLayer, id=$id');
            }
          }
        } catch (e) {
          debugPrint('🔍 Error querying all features: $e');
        }
      }
    } catch (e) {
      debugPrint('🔍 Error querying features: $e');
    }

    // Fallback to manual distance detection using cached real pins
    if (_currentPosition == null) return;

    // First try with real cached pins
    final pinsToCheck =
        _pinCache.isNotEmpty ? _pinCache.values.toList() : _allPinsData;

    if (pinsToCheck.isNotEmpty) {
      debugPrint(
          '🔍 Checking ${pinsToCheck.length} real pins for manual tap detection');
      for (final pinData in pinsToCheck) {
        final lat = pinData['latitude'] as double;
        final lng = pinData['longitude'] as double;
        final symbolPoint =
            await _mapController!.toScreenLocation(LatLng(lat, lng));
        final dx = (symbolPoint.x - point.x).abs();
        final dy = (symbolPoint.y - point.y).abs();
        final distance = math.sqrt(dx * dx + dy * dy);
        const threshold = 75.0; // Increased threshold for easier tapping
        debugPrint(
            '🔍 Real pin ${pinData['track_title'] ?? pinData['title']}: distance = $distance (threshold: $threshold)');
        if (distance <= threshold) {
          debugPrint(
              '🎯 Manual tap detected for real pin: ${pinData['track_title'] ?? pinData['title']}');
          _showPinDialog(pinData);
          return;
        }
      }
      debugPrint('🔍 No real pins found within tap threshold');
    }
  }

  // Clear cache when moving too far or cache is too old
  void _clearPinCache({bool force = false}) {
    if (force || _pinCache.length > 100) {
      // Limit cache size
      _pinCache.clear();
      _clusterCache.clear();
      _createdClusterIcons.clear(); // Clear cluster icon cache too
      _lastPinFetch = null;
      _lastFetchLat = null;
      _lastFetchLng = null;

      // Also clear test pins and real pin data
      _testPinsData.clear();
      _realPinData.removeWhere((key, value) =>
          key.startsWith('test_') ||
          value['id']?.toString().startsWith('test_') == true);

      debugPrint(
          '📍 Pin cache cleared (force: $force, size was: ${_pinCache.length})');
    }
  }

  // Clean old cluster cache entries to prevent memory leaks
  void _cleanClusterCache() {
    if (_clusterCache.length > 20) {
      // Keep only last 20 zoom levels
      final sortedKeys = _clusterCache.keys.toList()..sort();
      final keysToRemove = sortedKeys.take(_clusterCache.length - 15).toList();
      for (final key in keysToRemove) {
        _clusterCache.remove(key);
      }
      debugPrint(
          '📍 Cleaned cluster cache, removed ${keysToRemove.length} old entries');
    }

    // Also clean cluster icon cache if it gets too large
    if (_createdClusterIcons.length > 50) {
      final sortedIcons = _createdClusterIcons.toList()..sort();
      final iconsToKeep = sortedIcons.take(30).toSet();
      _createdClusterIcons.clear();
      _createdClusterIcons.addAll(iconsToKeep);
      debugPrint(
          '📍 Cleaned cluster icon cache, kept ${iconsToKeep.length} icons');
    }
  }

  // Validate pin state and log warnings if pins are missing
  void _validatePinState() {
    final currentPinCount = _testPinSymbols.length;
    final currentClusterCount = _clusterSymbols.length;
    final hasAnyVisible = currentPinCount > 0 || currentClusterCount > 0;
    final hasCachedData = _pinCache.isNotEmpty;

    // Log state changes
    if (currentPinCount != _lastKnownPinCount ||
        currentClusterCount != _lastKnownClusterCount) {
      debugPrint(
          '📍 Pin state change: Pins: $_lastKnownPinCount→$currentPinCount, Clusters: $_lastKnownClusterCount→$currentClusterCount');
      _lastKnownPinCount = currentPinCount;
      _lastKnownClusterCount = currentClusterCount;
    }

    // Warning if we have cached data but no visible pins
    if (hasCachedData && !hasAnyVisible) {
      debugPrint(
          '📍 ⚠️ WARNING: Have cached data (${_pinCache.length} pins) but no pins visible!');
      debugPrint(
          '📍 ⚠️ This suggests pins were cleared but not restored properly');
    }
  }

  // Ensure correct layer visibility after rebuild
  void _ensureCorrectLayerVisibility() async {
    if (_mapController == null) return;

    final currentZoom = _mapController!.cameraPosition?.zoom ?? _currentZoom;

    debugPrint('🌍 Ensuring correct layer visibility');
    debugPrint(
        '🌍 Current zoom from controller: $currentZoom, Max zoom for clustering: $_maxZoomForClustering');

    final shouldCluster = currentZoom < _maxZoomForClustering;

    try {
      // Ensure both individual and cluster sources are populated with all rendered pins
      debugPrint(
          '🌍 Updating sources before layer switch: rendered=${_renderedPinIds.length}, cached=${_pinCache.length}');

      await _updateIndividualPinsSource();
      await _updateClusterPinsSource();

      // Switch layer visibility based on zoom level
      await _switchLayerVisibility(shouldCluster);

      // Update layer state flags
      _currentlyShowingClusterLayer = shouldCluster;
      _isCurrentlyShowingClusters = shouldCluster;

      debugPrint(
          '🌍 Layer visibility set: ${shouldCluster ? 'Clusters' : 'Individual Pins'}');

      // CRITICAL: COMPLETELY DISABLE post-switch source refresh to prevent pin clearing
      // Layer switches should ONLY change visibility, NEVER call setGeoJsonSource
      // This was the main cause of pins disappearing during layer switches
      debugPrint('🌍 ✅ Layer switch completed - NO source refresh to preserve pins');

      // The following Timer-based refresh is PERMANENTLY DISABLED:
      // - It was calling setGeoJsonSource during layer switches
      // - This caused pins to be cleared and not properly restored
      // - Layer switches should only change layer visibility, not source data
    } catch (e) {
      debugPrint('🌍 Error ensuring layer visibility: $e');
    }
  }

  // Update individual pins source to ensure all pins are present
  Future<void> _updateIndividualPinsSource() async {
    if (_mapController == null) return;

    // CRITICAL FIX: Prevent source updates during layer switches or camera changes
    if (_shouldSkipSourceUpdate('individual pins source update')) {
      return;
    }

    // CRITICAL FIX: Prevent concurrent updates that cause pins to disappear
    if (_isUpdatingIndividualSource) {
      debugPrint('📍 ⚠️ Individual source update already in progress, skipping');
      return;
    }

    _isUpdatingIndividualSource = true;
    _lastSourceUpdate = DateTime.now();

    try {
      final features = <Map<String, dynamic>>[];

      // Use all rendered pins (includes both cached and progressively rendered pins)
      final allPinsToRender = <Map<String, dynamic>>[];

      // First, add all rendered pins from progressive rendering
      for (final pinId in _renderedPinIds) {
        final pin = _pinCache[pinId] ?? _realPinData[pinId];
        if (pin != null) {
          allPinsToRender.add(pin);
        }
      }

      // If no rendered pins yet, fall back to cached pins
      if (allPinsToRender.isEmpty) {
        allPinsToRender.addAll(_pinCache.values);
      }

      debugPrint(
          '🌍 Individual pins source: using ${allPinsToRender.length} pins (rendered: ${_renderedPinIds.length}, cached: ${_pinCache.length})');

      if (allPinsToRender.isEmpty) {
        debugPrint('🌍 ⚠️ WARNING: No pins to render in individual layer!');
        debugPrint('🌍 ⚠️ Rendered pin IDs: ${_renderedPinIds.toList()}');
        debugPrint('🌍 ⚠️ Cache keys: ${_pinCache.keys.toList()}');
        debugPrint('🌍 ⚠️ Real pin data keys: ${_realPinData.keys.toList()}');
      }

      for (final pinData in allPinsToRender) {
        final lat = pinData['latitude'] as double?;
        final lng = pinData['longitude'] as double?;

        if (lat == null || lng == null) continue;

        final rarity = _getPinRarity(pinData);
        final pinId = pinData['id']?.toString() ?? '';

        // Determine icon image with custom skin support
        String iconImageId = 'music-pin-$rarity';

        // Check for custom skin and add if available
        String? skinImageUrl;
        if (pinData['skinDetails'] != null &&
            pinData['skinDetails']['image'] != null) {
          skinImageUrl = pinData['skinDetails']['image'].toString();
        }

        if (skinImageUrl != null && skinImageUrl.isNotEmpty) {
          final customImageId = 'pin-skin-$pinId';
          try {
            final added = await _addSkinImage(
                imageUrl: skinImageUrl, imageId: customImageId);
            if (added) {
              iconImageId = customImageId;
              debugPrint('🌍 Using custom skin for individual pin $pinId');
            }
          } catch (e) {
            debugPrint('🌍 Failed to add custom skin for pin $pinId: $e');
          }
        }

        features.add({
          'type': 'Feature',
          'geometry': {
            'type': 'Point',
            'coordinates': [lng, lat],
          },
          'properties': {
            'id': pinId,
            'rarity': rarity,
            'icon': iconImageId,
          },
        });
      }

      await _mapController!.setGeoJsonSource(
        'individual-pins-source',
        {
          'type': 'FeatureCollection',
          'features': features,
        },
      );

      debugPrint(
          '🌍 Updated individual pins source with ${features.length} pins');
    } catch (e) {
      debugPrint('🌍 Error updating individual pins source: $e');
    } finally {
      _isUpdatingIndividualSource = false;
    }
  }

  // Update cluster pins source to ensure all clusters are present
  Future<void> _updateClusterPinsSource() async {
    if (_mapController == null || _pinCache.isEmpty) return;

    // CRITICAL FIX: Prevent source updates during layer switches or camera changes
    if (_shouldSkipSourceUpdate('cluster pins source update')) {
      return;
    }

    // CRITICAL FIX: Prevent concurrent updates that cause pins to disappear
    if (_isUpdatingClusterSource) {
      debugPrint('📍 ⚠️ Cluster source update already in progress, skipping');
      return;
    }

    _isUpdatingClusterSource = true;
    _lastSourceUpdate = DateTime.now();

    try {
      // Recompute clusters based on current zoom level
      final pinsData = _pinCache.values.toList();
      final clusters = await ClusterService.instance.computeClusters(
        pins: _allPinsData,
        clusterRadius: 250.0,
        minClusterSize: 3,
      );

      final features = <Map<String, dynamic>>[];

      for (final cluster in clusters) {
        final lat = cluster['latitude'] as double;
        final lng = cluster['longitude'] as double;

        if (cluster['isCluster'] == true) {
          final count = cluster['count'] as int;
          features.add({
            'type': 'Feature',
            'geometry': {
              'type': 'Point',
              'coordinates': [lng, lat],
            },
            'properties': {
              'id': cluster['id'],
              'count': count,
              'icon': 'cluster-icon-$count',
            },
          });
        }
      }

      await _mapController!.setGeoJsonSource(
        'cluster-pins-source',
        {
          'type': 'FeatureCollection',
          'features': features,
        },
      );

      debugPrint(
          '🌍 Updated cluster pins source with ${features.length} clusters');
    } catch (e) {
      debugPrint('🌍 Error updating cluster pins source: $e');
    } finally {
      _isUpdatingClusterSource = false;
    }
  }

  // Start scanning animation for pin detection
  void _startScanningAnimation() {
    if (_mapController == null || _currentPosition == null) return;

    debugPrint('🔍 Starting scanning animation');
    _isScanningActive = true;
    _scanningAnimationController.repeat();

    // Add scanning effect to map
    _addScanningEffect();
  }

  // Stop scanning animation
  void _stopScanningAnimation() {
    debugPrint('🔍 Stopping scanning animation');
    _isScanningActive = false;
    _scanningAnimationController.stop();
    _scanningAnimationController.reset();
    _scanningEffectTimer?.cancel();

    // Remove scanning effect from map
    _removeScanningEffect();
  }

  // Add radar-style scanning effect to the map
  void _addScanningEffect() {
    if (_mapController == null || _currentPosition == null) return;

    _scanningEffectTimer?.cancel();

    // Create animated radar sweep
    _scanningEffectTimer =
        Timer.periodic(const Duration(milliseconds: 50), (timer) {
      if (!mounted || _mapController == null || !_isScanningActive) {
        timer.cancel();
        return;
      }

      _updateScanningEffect();
    });
  }

  // Update the scanning radar effect
  void _updateScanningEffect() {
    if (_mapController == null || _currentPosition == null) return;

    try {
      final animationValue = _scanningAnimation.value;
      final sweepAngle = animationValue * 360.0; // Full rotation
      final maxRadius = 100.0; // Scanning radius in meters

      // Create radar sweep effect
      final sweepFeatures = <Map<String, dynamic>>[];

      // Create the main sweep line
      final sweepLineFeatures = _createRadarSweepLine(
        _currentPosition!.latitude,
        _currentPosition!.longitude,
        sweepAngle,
        maxRadius,
      );
      sweepFeatures.addAll(sweepLineFeatures);

      // Create the scanning circle
      final scanCircle = _createCirclePolygon(
        _currentPosition!.latitude,
        _currentPosition!.longitude,
        maxRadius,
      );

      sweepFeatures.add({
        'type': 'Feature',
        'geometry': scanCircle,
        'properties': {
          'id': 'scan-circle',
          'type': 'circle',
        },
      });

      // Update or create the scanning source
      _mapController!.setGeoJsonSource(
        'scanning-source',
        {
          'type': 'FeatureCollection',
          'features': sweepFeatures,
        },
      ).catchError((e) {
        // Source doesn't exist yet, create it
        _mapController!
            .addSource(
          'scanning-source',
          GeojsonSourceProperties(
            data: {
              'type': 'FeatureCollection',
              'features': sweepFeatures,
            },
          ),
        )
            .then((_) {
          // Add scanning layers
          _addScanningLayers();
        }).catchError((createError) {
          debugPrint('🔍 Error creating scanning source: $createError');
        });
      });
    } catch (e) {
      debugPrint('🔍 Error updating scanning effect: $e');
    }
  }

  // Create radar sweep line features
  List<Map<String, dynamic>> _createRadarSweepLine(
    double centerLat,
    double centerLng,
    double angle,
    double radius,
  ) {
    final features = <Map<String, dynamic>>[];

    // Convert angle to radians
    final angleRad = (angle - 90) * (math.pi / 180); // -90 to start from top

    // Calculate end point of sweep line
    const double earthRadius = 6371000;
    final latDelta = (radius / earthRadius) * (180 / math.pi);
    final lngDelta = (radius / earthRadius) *
        (180 / math.pi) /
        math.cos(centerLat * math.pi / 180);

    final endLat = centerLat + (latDelta * math.cos(angleRad));
    final endLng = centerLng + (lngDelta * math.sin(angleRad));

    // Create the main sweep line (bright)
    features.add({
      'type': 'Feature',
      'geometry': {
        'type': 'LineString',
        'coordinates': [
          [centerLng, centerLat],
          [endLng, endLat],
        ],
      },
      'properties': {
        'id': 'sweep-line',
        'type': 'sweep',
        'opacity': 0.8,
      },
    });

    // Create fade trail behind the sweep (3 trailing lines with decreasing opacity)
    for (int i = 1; i <= 3; i++) {
      final trailAngle = angle - (i * 15); // 15 degrees apart
      final trailAngleRad = (trailAngle - 90) * (math.pi / 180);

      final trailEndLat = centerLat + (latDelta * math.cos(trailAngleRad));
      final trailEndLng = centerLng + (lngDelta * math.sin(trailAngleRad));

      features.add({
        'type': 'Feature',
        'geometry': {
          'type': 'LineString',
          'coordinates': [
            [centerLng, centerLat],
            [trailEndLng, trailEndLat],
          ],
        },
        'properties': {
          'id': 'sweep-trail-$i',
          'type': 'trail',
          'opacity': 0.8 / (i + 1), // Decreasing opacity
        },
      });
    }

    return features;
  }

  // Add scanning effect layers to the map
  void _addScanningLayers() {
    if (_mapController == null) return;

    try {
      // Add radar sweep line layer (no belowLayerId = topmost layer)
      _mapController!.addLineLayer(
        'scanning-source',
        'scanning-sweep-layer',
        const LineLayerProperties(
          lineColor: '#00FFFF', // Cyan color for the sweep
          lineWidth: [
            'case',
            [
              '==',
              ['get', 'type'],
              'sweep'
            ],
            3.0,
            2.0, // Thinner for trails
          ],
          lineOpacity: ['get', 'opacity'],
          lineBlur: 1.0,
        ),
        filter: [
          'in',
          ['get', 'type'],
          [
            'literal',
            ['sweep', 'trail']
          ]
        ],
        // No belowLayerId parameter = adds to very top of layer stack
      ).catchError((e) => debugPrint('🔍 Error adding sweep layer: $e'));

      // Add scanning circle border (no belowLayerId = topmost layer)
      _mapController!.addLineLayer(
        'scanning-source',
        'scanning-circle-layer',
        const LineLayerProperties(
          lineColor: '#00FFFF',
          lineWidth: 2.0,
          lineOpacity: 0.3,
          lineDasharray: [5, 5], // Dashed line
        ),
        filter: [
          '==',
          ['get', 'type'],
          'circle'
        ],
        // No belowLayerId parameter = adds to very top of layer stack
      ).catchError((e) => debugPrint('🔍 Error adding circle layer: $e'));

      debugPrint('🔍 Added scanning layers on top of all other layers');
    } catch (e) {
      debugPrint('🔍 Error adding scanning layers: $e');
    }
  }

  // Remove scanning effect from map
  void _removeScanningEffect() {
    if (_mapController == null) return;

    try {
      // Remove scanning layers
      _mapController!
          .removeLayer('scanning-sweep-layer')
          .catchError((e) => null);
      _mapController!
          .removeLayer('scanning-circle-layer')
          .catchError((e) => null);

      // Remove scanning source
      _mapController!.removeSource('scanning-source').catchError((e) => null);

      debugPrint('🔍 Removed scanning layers');
    } catch (e) {
      debugPrint('🔍 Error removing scanning layers: $e');
    }
  }

  // Start monitoring pin state to detect disappearing pins
  void _startPinStateMonitoring() {
    _pinStateMonitor?.cancel();

    // Check pin state every 5 seconds
    _pinStateMonitor = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (!mounted || _mapController == null) {
        timer.cancel();
        return;
      }

      _validatePinState();

      // If we have cached data but no visible pins, try to restore them
      final hasAnyVisible =
          _testPinSymbols.isNotEmpty || _clusterSymbols.isNotEmpty;
      final hasCachedData = _pinCache.isNotEmpty;

      // Only attempt recovery if:
      // 1. We have cached data but no visible pins
      // 2. No pin fetch or progressive rendering is in progress
      // 3. Progressive rendering is not active (timer not running)
      if (hasCachedData &&
          !hasAnyVisible &&
          !_isPinFetchInProgress &&
          !(_progressiveUpdateTimer?.isActive ?? false)) {
        // Add a delay before recovery to allow progressive rendering to complete
        Future.delayed(const Duration(seconds: 2), () {
          // Double check state hasn't changed during delay
          if (mounted &&
              hasCachedData &&
              !hasAnyVisible &&
              !_isPinFetchInProgress &&
              !(_progressiveUpdateTimer?.isActive ?? false)) {
            debugPrint(
                '📍 🔧 Auto-recovery: Attempting to restore missing pins');
            _updateMapPins().catchError((e) {
              debugPrint('📍 Auto-recovery failed: $e');
            });
          } else {
            debugPrint(
                '📍 🔧 Auto-recovery skipped - state changed during delay');
          }
        });
      }
    });
  }

  // Switch layer visibility for instant cluster/pin switching
  bool _isSwitchingLayers = false;
  DateTime? _lastLayerSwitch;
  static const Duration _layerSwitchCooldown = Duration(milliseconds: 200);

  Future<void> _switchLayerVisibility(bool showClusters,
      {bool useFallback = true}) async {
    if (_mapController == null || !mounted || _isSwitchingLayers) return;

    // Prevent rapid layer switching that can cause disappearing pins
    final now = DateTime.now();
    if (_lastLayerSwitch != null &&
        now.difference(_lastLayerSwitch!) < _layerSwitchCooldown) {
      debugPrint('📍 Layer switch cooldown active, skipping');
      return;
    }
    _lastLayerSwitch = now;

    _isSwitchingLayers = true;

    try {
      // Only skip layer visibility switch if we're trying to show layers but have no data
      // Always allow hiding layers (when showClusters is being set to hide layers)
      if (_allPinsData.isEmpty && _pinCache.isEmpty) {
        debugPrint('📍 No pins available - will still process layer visibility for hiding/showing empty layers');
        // Don't return early - we still need to set layer visibility properly
      }

      debugPrint('📍 Enhanced layer switching: showClusters=$showClusters');
      debugPrint(
          '📍 Current zoom: $_currentZoom, clustering threshold: $_maxZoomForClustering');
      debugPrint('📍 Current layer state: ${_layerStateMachine.currentState}');

      // Enhanced state machine validation for progressive rendering
      final targetState = showClusters
          ? LayerState.showingClusters
          : LayerState.showingIndividual;

      // Check if we're in progressive rendering mode
      final isProgressiveRendering =
          _isPinFetchInProgress && _isProgressiveRenderingEnabled;

      debugPrint(
          '📍 Progressive rendering check: inProgress=$_isPinFetchInProgress, enabled=$_isProgressiveRenderingEnabled, pending=${_pendingPins.length}');

      // Allow clusters to show immediately during progressive rendering
      // using already rendered pins - no need to wait for completion
      if (isProgressiveRendering && showClusters) {
        debugPrint(
            '📍 🚀 Cluster layer requested during progressive rendering - using already rendered pins');

        // If we have some rendered pins, create clusters from them immediately
        if (_renderedPinIds.isNotEmpty) {
          debugPrint(
              '📍 🚀 Creating immediate clusters from ${_renderedPinIds.length} rendered pins');

          // Rebuild cluster layer with current rendered pins (non-blocking)
          _rebuildClusterLayerWithAllPins().catchError((e) {
            debugPrint('📍 ⚠️ Error creating immediate clusters: $e');
          });
        }
      }

      // Use state machine for reliable layer management with retry logic
      bool transitionSuccess = false;
      int retryCount = 0;
      const maxRetries = 3;

      while (!transitionSuccess && retryCount < maxRetries) {
        transitionSuccess =
            await _layerStateMachine.transitionTo(targetState, context: {
          'zoom': _currentZoom,
          'showClusters': showClusters,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
          'progressiveRendering': isProgressiveRendering,
          'pendingPins': _pendingPins.length,
          'retryAttempt': retryCount,
        });

        if (!transitionSuccess) {
          retryCount++;
          debugPrint(
              '📍 ⚠️ State machine transition failed (attempt $retryCount/$maxRetries)');
          if (retryCount < maxRetries) {
            // Wait briefly before retry
            await Future.delayed(Duration(milliseconds: 100 * retryCount));
          }
        }
      }

      if (!transitionSuccess) {
        debugPrint(
            '📍 ❌ State machine transition failed after $maxRetries attempts, falling back to direct layer switch');
        // Fall back to direct layer switching if state machine fails
        await _performDirectLayerSwitch(showClusters, useFallback);
        return;
      }

      // Perform the actual layer switching based on state machine validation
      await _performDirectLayerSwitch(showClusters, useFallback);

      // Validate final visibility to catch any inconsistencies
      await _validateLayerVisibility(showClusters);

      // Update the state to reflect which layer is currently shown
      _currentlyShowingClusterLayer = showClusters;
      _isCurrentlyShowingClusters = showClusters;
    } catch (e) {
      debugPrint('📍 ❌ Error during layer visibility switch: $e');
      // Attempt recovery by validating and correcting layer state
      try {
        await _layerStateMachine.validateAndCorrect();
      } catch (recoveryError) {
        debugPrint('📍 ❌ Recovery failed: $recoveryError');
      }
    } finally {
      _isSwitchingLayers = false;

      // Validate final state to ensure layers are visible
      Timer(const Duration(milliseconds: 500), () {
        if (mounted && _mapController != null) {
          _validateLayerVisibility(showClusters);
        }
      });
    }
  }

  // Validate that the correct layers are visible after switching
  Future<void> _validateLayerVisibility(bool shouldShowClusters) async {
    if (_mapController == null) return;

    try {
      // Check if the expected layers are actually visible
      final expectedIndividualVisibility = !shouldShowClusters;
      final expectedClusterVisibility = shouldShowClusters;

      debugPrint(
          '📍 🔍 Validating layer visibility: individual=$expectedIndividualVisibility, cluster=$expectedClusterVisibility');

      // If validation fails, attempt to correct the visibility
      await _mapController!.setLayerVisibility(
          'individual-pins-layer', expectedIndividualVisibility);
      await _mapController!
          .setLayerVisibility('cluster-pins-layer', expectedClusterVisibility);

      debugPrint('📍 ✅ Layer visibility validated and corrected if needed');
    } catch (e) {
      debugPrint('📍 ❌ Layer visibility validation failed: $e');
    }
  }

  /// CRITICAL: Check if source updates should be avoided to prevent pin clearing
  bool _shouldSkipSourceUpdate(String reason) {
    // Skip source updates during layer switches to prevent pin disappearing
    if (_isSwitchingLayers) {
      debugPrint('📍 ⚠️ Skipping source update during layer switch: $reason');
      return true;
    }

    // Skip if progressive rendering is active
    if (_isProgressiveRenderingActive || _isPinFetchInProgress || _progressiveUpdateTimer?.isActive == true) {
      debugPrint('📍 ⚠️ Skipping source update during progressive rendering: $reason');
      return true;
    }

    // Skip if another source update happened very recently
    if (_lastSourceUpdate != null &&
        DateTime.now().difference(_lastSourceUpdate!) < const Duration(milliseconds: 100)) {
      debugPrint('📍 ⚠️ Skipping source update - too frequent: $reason');
      return true;
    }

    return false;
  }

  /// Direct layer switching method (fallback for state machine)
  /// CRITICAL: This method ONLY changes layer visibility, NEVER clears pins
  Future<void> _performDirectLayerSwitch(
      bool showClusters, bool useFallback) async {
    final stopwatch = Stopwatch()..start();

    // CRITICAL: Mark that we're switching layers to prevent source updates
    _shouldAvoidSourceUpdates = true;

    if (showClusters) {
      // Parallelize all layer visibility changes for instant switching
      await Future.wait([
        // Main pin layers
        _mapController!.setLayerVisibility('individual-pins-layer', false),
        _mapController!.setLayerVisibility('cluster-pins-layer', true),

        // Keep individual glow layers ALWAYS visible for persistent aura effect!
        // These contain the properly rendered glow from progressive rendering
        _mapController!.setLayerVisibility('individual-glow-fill-0', true),
        _mapController!.setLayerVisibility('individual-glow-fill-1', true),
        _mapController!
            .setLayerVisibility('individual-aura-border-layer', true),
        _mapController!.setLayerVisibility('individual-glow-pulse-layer', true),

        // Hide cluster glow layers (not needed, using individual glow)
        _mapController!.setLayerVisibility('cluster-glow-fill-0', false),
        _mapController!.setLayerVisibility('cluster-glow-fill-1', false),
        _mapController!.setLayerVisibility('cluster-aura-border-layer', false),
        _mapController!.setLayerVisibility('cluster-glow-pulse-layer', false),
      ]);

      debugPrint('📍 ✅ Showing cluster layers, hiding individual layers');

      // Handle fallback symbols after main layers
      await _toggleFallbackSymbols(showClusters);
    } else {
      // Parallelize all layer visibility changes for instant switching
      await Future.wait([
        // Main pin layers
        _mapController!.setLayerVisibility('individual-pins-layer', true),
        _mapController!.setLayerVisibility('cluster-pins-layer', false),

        // Individual glow layers (show)
        _mapController!.setLayerVisibility('individual-glow-fill-0', true),
        _mapController!.setLayerVisibility('individual-glow-fill-1', true),
        _mapController!
            .setLayerVisibility('individual-aura-border-layer', true),
        _mapController!.setLayerVisibility('individual-glow-pulse-layer', true),

        // Cluster glow layers (hide)
        _mapController!.setLayerVisibility('cluster-glow-fill-0', false),
        _mapController!.setLayerVisibility('cluster-glow-fill-1', false),
        _mapController!.setLayerVisibility('cluster-aura-border-layer', false),
        _mapController!.setLayerVisibility('cluster-glow-pulse-layer', false),
      ]);

      debugPrint('📍 ✅ Showing individual layers, hiding cluster layers');

      // Handle fallback symbols after main layers
      await _toggleFallbackSymbols(showClusters);
    }

    stopwatch.stop();
    debugPrint(
        '📍 ⚡ Layer switch completed in ${stopwatch.elapsedMilliseconds}ms (parallelized)');

    // CRITICAL: Reset the flag to allow source updates again
    _shouldAvoidSourceUpdates = false;
  }

  // Toggle visibility of legacy Symbol-based pins/clusters used by the fallback
  // rendering path by adjusting their icon opacity. This helps ensure that
  // individual pins do not remain visible when we intend to show only clusters
  // and vice-versa on the first map load before layer-based pins initialise.
  Future<void> _toggleFallbackSymbols(bool showClusters) async {
    if (_mapController == null) return;

    // If there are no legacy symbols, nothing to do
    if (_testPinSymbols.isEmpty && _clusterSymbols.isEmpty) return;

    final List<Future> tasks = [];

    // Individual pin symbols (in _testPinSymbols) should be hidden when clusters
    // are shown, and displayed when clusters are hidden.
    for (final symbol in _testPinSymbols) {
      tasks.add(_mapController!
          .updateSymbol(
              symbol, SymbolOptions(iconOpacity: showClusters ? 0.0 : 1.0))
          .catchError((_) {}));
    }

    // Cluster symbols behave inversely.
    for (final symbol in _clusterSymbols) {
      tasks.add(_mapController!
          .updateSymbol(
              symbol, SymbolOptions(iconOpacity: showClusters ? 1.0 : 0.0))
          .catchError((_) {}));
    }

    if (tasks.isNotEmpty) {
      await Future.wait(tasks);
    }
  }

  // Create individual pins layer using GeoJSON source
  Future<void> _createIndividualPinsLayer(
      List<Map<String, dynamic>> pinData) async {
    if (_mapController == null) return;

    // Ensure any previous individual pin layer/source are fully removed to avoid
    // "source already exists" platform errors that could happen when switching
    // filters very quickly or if a previous removal silently failed.
    try {
      await _mapController!
          .removeLayer('individual-pins-layer')
          .catchError((_) {});
      await _mapController!
          .removeSource('individual-pins-source')
          .catchError((_) {});
    } catch (_) {/* noop */}

    debugPrint('🎨 ===== CREATING INDIVIDUAL PINS LAYER =====');
    debugPrint('🎨 Creating layer with ${pinData.length} pins');

    // Create GeoJSON features for individual pins with proper icon resolution
    final features = <Map<String, dynamic>>[];

    for (final pin in pinData) {
      final pinId = pin['id']?.toString() ?? '';

      // Parse location coordinates - handle both direct lat/lng and location object
      double lat, lng;
      if (pin['latitude'] != null && pin['longitude'] != null) {
        lat = pin['latitude'] as double;
        lng = pin['longitude'] as double;
      } else if (pin['location'] != null) {
        final location = pin['location'];
        if (location is Map<String, dynamic> &&
            location['coordinates'] != null) {
          // GeoJSON format: [longitude, latitude]
          final coords = location['coordinates'] as List;
          lng = coords[0] as double;
          lat = coords[1] as double;
        } else if (location is String && location.contains('POINT')) {
          // Parse SRID=4326;POINT (lng lat) format
          final coordsStr = location.split('POINT ')[1].trim();
          final coords = coordsStr
              .replaceAll('(', '')
              .replaceAll(')', '')
              .split(' ')
              .map((s) => double.parse(s))
              .toList();
          lng = coords[0];
          lat = coords[1];
        } else {
          debugPrint('🎨 ❌ Invalid location format for pin $pinId: $location');
          continue;
        }
      } else {
        debugPrint('🎨 ❌ No location data for pin $pinId');
        continue;
      }

      final rarity = _getPinRarity(pin);

      debugPrint(
          '🎨 Processing pin $pinId at ($lat, $lng) with rarity: $rarity');

      // Determine icon image (same logic as _addOptimizedIndividualPin)
      String iconImageId = 'music-pin-$rarity';

      // Try to add custom skin image if available
      String? skinImageUrl;
      if (pin['skinDetails'] != null && pin['skinDetails']['image'] != null) {
        skinImageUrl = pin['skinDetails']['image'].toString();
      } else if (mounted) {
        try {
          final skinProvider =
              Provider.of<SkinProvider>(context, listen: false);
          final matched = skinProvider.availableSkins.firstWhere(
            (s) => s.id == pin['skin'],
            orElse: () => PinSkin(
              id: -1,
              name: 'Unknown',
              image: '',
              createdAt: DateTime.now(),
              unlockRule: 'FREE',
              locked: false,
            ),
          );
          if (matched.id != -1 && matched.image.isNotEmpty) {
            skinImageUrl = matched.image;
          }
        } catch (e) {
          debugPrint('Error getting skin provider: $e');
        }
      }

      // Add custom skin image if available
      if (skinImageUrl != null && skinImageUrl.isNotEmpty) {
        final customImageId = 'pin-skin-$pinId';
        try {
          final added = await _addSkinImage(
              imageUrl: skinImageUrl, imageId: customImageId);
          if (added) {
            iconImageId = customImageId;
            debugPrint('📍 Using custom glassmorphism skin for pin $pinId');
          }
        } catch (e) {
          debugPrint('📍 Failed to add custom skin for pin $pinId: $e');
        }
      }

      // Determine if this pin is currently playing
      final isPlaying =
          _currentlyPlayingPinId != null && _currentlyPlayingPinId == pinId;

      features.add({
        'type': 'Feature',
        'geometry': {
          'type': 'Point',
          'coordinates': [lng, lat],
        },
        'properties': {
          'id': pinId,
          'rarity': rarity,
          'icon': iconImageId,
          'isPlaying': isPlaying,
        },
      });
    }

    // Add source
    await _mapController!.addSource(
      'individual-pins-source',
      GeojsonSourceProperties(
        data: {
          'type': 'FeatureCollection',
          'features': features,
        },
      ),
    );

    // Add layer at the very top (no belowLayerId = topmost layer)
    await _mapController!.addSymbolLayer(
      'individual-pins-source',
      'individual-pins-layer',
      const SymbolLayerProperties(
        iconImage: ['get', 'icon'],
        iconSize: [
          'case',
          ['get', 'isPlaying'],
          1.96, // 1.4 * 1.4 = 1.96 for playing pins (40% larger)
          1.4 // Default size for non-playing pins
        ],
        iconAnchor: 'bottom',
        iconAllowOverlap: true,
        iconIgnorePlacement: true,
      ),
      // No belowLayerId parameter = adds to very top of layer stack
    );

    debugPrint(
        '🎨 ✅ Added individual-pins-layer with ${features.length} features');
    debugPrint(
        '🎨 Individual layer features: ${features.map((f) => f['properties']['id']).take(5).toList()}...');

    // Only clear and rebuild symbol data if progressive rendering is not active
    // During progressive rendering, _testPinSymbols is managed by the progressive system
    if (!_isProgressiveRenderingActive && !_isPinFetchInProgress && !(_progressiveUpdateTimer?.isActive == true)) {
      debugPrint('🎨 Clearing and rebuilding _testPinSymbols (progressive rendering not active)');
      _testPinSymbols.clear();
    } else {
      debugPrint('🎨 Skipping _testPinSymbols rebuild - progressive rendering is active');
    }

    // Update _realPinData for tap handling - ensure IDs match exactly
    for (final pin in pinData) {
      final pinId = pin['id']?.toString() ?? '';
      if (pinId.isNotEmpty) {
        // Create a copy of pin data with properly extracted coordinates
        final pinWithCoords = Map<String, dynamic>.from(pin);

        // Parse location properly - handle null lat/lng
        double? latitude = pin['latitude'];
        double? longitude = pin['longitude'];

        // If lat/lng are null, try to parse from location field
        if (latitude == null || longitude == null) {
          final location = pin['location'];
          if (location != null) {
            if (location is String && location.contains('POINT')) {
              // Parse SRID=4326;POINT (lng lat) format - PostGIS format
              try {
                final coordsStr = location.split('POINT ')[1].trim();
                final coords = coordsStr
                    .replaceAll('(', '')
                    .replaceAll(')', '')
                    .split(' ')
                    .map((s) => double.parse(s))
                    .toList();
                longitude = coords[0];
                latitude = coords[1];
                debugPrint(
                    '🎨 Parsed PostGIS location: lat=$latitude, lng=$longitude');

                // Update the pin data copy with extracted coordinates
                pinWithCoords['latitude'] = latitude;
                pinWithCoords['longitude'] = longitude;
              } catch (e) {
                debugPrint('🎨 Failed to parse PostGIS location: $e');
              }
            } else if (location is Map<String, dynamic> &&
                location['type'] == 'Point' &&
                location['coordinates'] != null) {
              // Parse GeoJSON format: {type: "Point", coordinates: [lng, lat]}
              try {
                final coordinates = location['coordinates'] as List;
                longitude = coordinates[0] as double;
                latitude = coordinates[1] as double;
                debugPrint(
                    '🎨 Parsed GeoJSON location: lat=$latitude, lng=$longitude');

                // Update the pin data copy with extracted coordinates
                pinWithCoords['latitude'] = latitude;
                pinWithCoords['longitude'] = longitude;
              } catch (e) {
                debugPrint('🎨 Failed to parse GeoJSON location: $e');
              }
            }
          }
        }

        // Add the pin data with coordinates to _realPinData
        _realPinData[pinId] = pinWithCoords;
        debugPrint('🎨 Added pin data for tap handling: $pinId');

        // Only create symbol if we have valid coordinates AND progressive rendering is not managing symbols
        if (latitude != null && longitude != null) {
          // Only add to _testPinSymbols if progressive rendering is not active
          if (!_isProgressiveRenderingActive && !_isPinFetchInProgress && !(_progressiveUpdateTimer?.isActive == true)) {
            // Create dummy symbol for compatibility
            final symbol = Symbol(
              pinId,
              SymbolOptions(
                geometry: LatLng(latitude, longitude),
              ),
            );
            _testPinSymbols.add(symbol);
            debugPrint(
                '🎨 ✅ Created symbol for pin $pinId with coordinates: lat=$latitude, lng=$longitude');
          } else {
            debugPrint(
                '🎨 ⏭️ Skipping symbol creation for pin $pinId - progressive rendering is managing symbols');
          }
        } else {
          debugPrint(
              '🎨 ⚠️ Skipping pin $pinId - invalid coordinates: lat=$latitude, lng=$longitude');
        }
      }
    }

    debugPrint(
        '🎨 ✅ Created individual pins layer with ${features.length} pins (with glassmorphism skins)');
    debugPrint('🎨 Final _testPinSymbols count: ${_testPinSymbols.length}');
    debugPrint('🎨 Final _realPinData count: ${_realPinData.length}');
    debugPrint('🎨 Final _realPinData keys: ${_realPinData.keys.toList()}');
    debugPrint('🎨 ===== INDIVIDUAL PINS LAYER COMPLETE =====');
  }

  // Create cluster layer using GeoJSON source
  Future<void> _createClusterLayer(List<Map<String, dynamic>> clusters) async {
    if (_mapController == null) return;

    // Ensure any previous cluster layer/source are fully removed
    try {
      await _mapController!
          .removeLayer('cluster-pins-layer')
          .catchError((_) {});
      await _mapController!
          .removeSource('cluster-pins-source')
          .catchError((_) {});
    } catch (_) {/* noop */}

    if (clusters.isEmpty) {
      // Create empty cluster layer
      await _mapController!.addSource(
        'cluster-pins-source',
        const GeojsonSourceProperties(
          data: {
            'type': 'FeatureCollection',
            'features': [],
          },
        ),
      );

      await _mapController!.addSymbolLayer(
        'cluster-pins-source',
        'cluster-pins-layer',
        const SymbolLayerProperties(
          iconImage: 'cluster-icon-2',
          iconSize: 1.4,
          iconAnchor: 'bottom',
          iconAllowOverlap: true,
          iconIgnorePlacement: true,
        ),
        // No belowLayerId parameter = adds to very top of layer stack
      );
      debugPrint('📍 Created empty cluster layer');
      return;
    }

    debugPrint('📍 Creating cluster layer with ${clusters.length} items:');
    debugPrint(
        '📍 - Actual clusters: ${clusters.where((c) => c['isCluster'] == true).length}');
    debugPrint(
        '📍 - Individual pins: ${clusters.where((c) => c['isCluster'] != true).length}');

    // Get needed cluster icons
    final neededCounts = clusters
        .where((c) => c['isCluster'] == true)
        .map((c) => c['count'] as int)
        .toSet();
    await _addNeededClusterIcons(neededCounts);

    // Create GeoJSON features for clusters
    final features = <Map<String, dynamic>>[];
    _clusterSymbols.clear();
    _clusters.clear();

    for (final cluster in clusters) {
      final lat = cluster['latitude'] as double;
      final lng = cluster['longitude'] as double;

      if (cluster['isCluster'] == true) {
        final count = cluster['count'] as int;
        features.add({
          'type': 'Feature',
          'geometry': {
            'type': 'Point',
            'coordinates': [lng, lat],
          },
          'properties': {
            'id': cluster['id'],
            'count': count,
            'icon': 'cluster-icon-$count',
          },
        });

        // Keep cluster data for tap handling
        final symbol = Symbol(
          cluster['id'],
          SymbolOptions(
            geometry: LatLng(lat, lng),
          ),
        );
        _clusterSymbols.add(symbol);
        _clusters.add(cluster);
      } else {
        // Individual pins within cluster view (use same icon resolution as individual layer)
        final pinData = cluster['pin'] as Map<String, dynamic>;
        final rarity = _getPinRarity(pinData);
        final pinId = pinData['id']?.toString() ?? '';

        debugPrint(
            '📍 Processing individual pin in cluster layer: $pinId (rarity: $rarity)');

        // Determine icon image (same logic as individual pins)
        String iconImageId = 'music-pin-$rarity';

        // Try to add custom skin image if available
        String? skinImageUrl;
        if (pinData['skinDetails'] != null &&
            pinData['skinDetails']['image'] != null) {
          skinImageUrl = pinData['skinDetails']['image'].toString();
        } else if (mounted) {
          try {
            final skinProvider =
                Provider.of<SkinProvider>(context, listen: false);
            final matched = skinProvider.availableSkins.firstWhere(
              (s) => s.id == pinData['skin'],
              orElse: () => PinSkin(
                id: -1,
                name: 'Unknown',
                image: '',
                createdAt: DateTime.now(),
                unlockRule: 'FREE',
                locked: false,
              ),
            );
            if (matched.id != -1 && matched.image.isNotEmpty) {
              skinImageUrl = matched.image;
            }
          } catch (e) {
            debugPrint('Error getting skin provider for cluster pin: $e');
          }
        }

        // Add custom skin image if available
        if (skinImageUrl != null && skinImageUrl.isNotEmpty) {
          final customImageId = 'pin-skin-$pinId';
          try {
            final added = await _addSkinImage(
                imageUrl: skinImageUrl, imageId: customImageId);
            if (added) {
              iconImageId = customImageId;
              debugPrint(
                  '📍 Using custom glassmorphism skin for cluster pin $pinId');
            }
          } catch (e) {
            debugPrint(
                '📍 Failed to add custom skin for cluster pin $pinId: $e');
          }
        }

        // Determine if this pin is currently playing
        final isPlaying = _currentlyPlayingPinId != null &&
            _currentlyPlayingPinId == pinData['id']?.toString();

        features.add({
          'type': 'Feature',
          'geometry': {
            'type': 'Point',
            'coordinates': [lng, lat],
          },
          'properties': {
            'id': pinData['id'],
            'rarity': rarity,
            'icon': iconImageId,
            'isPlaying': isPlaying,
          },
        });

        debugPrint(
            '📍 ✅ Added individual pin $pinId to cluster layer features');
      }
    }

    // Add source
    await _mapController!.addSource(
      'cluster-pins-source',
      GeojsonSourceProperties(
        data: {
          'type': 'FeatureCollection',
          'features': features,
        },
      ),
    );

    // Add layer at the very top (no belowLayerId = topmost layer)
    await _mapController!.addSymbolLayer(
      'cluster-pins-source',
      'cluster-pins-layer',
      const SymbolLayerProperties(
        iconImage: ['get', 'icon'],
        iconSize: [
          'case',
          ['get', 'isPlaying'],
          1.96, // 1.4 * 1.4 = 1.96 for playing pins (40% larger)
          1.4 // Default size for non-playing pins
        ],
        iconAnchor: 'bottom',
        iconAllowOverlap: true,
        iconIgnorePlacement: true,
      ),
      // No belowLayerId parameter = adds to very top of layer stack
    );

    debugPrint(
        '📍 ✅ Added cluster-pins-layer with ${features.length} features');
    if (features.isNotEmpty) {
      final clusterFeatures =
          features.where((f) => f['properties']['count'] != null).length;
      final individualFeatures =
          features.where((f) => f['properties']['count'] == null).length;
      debugPrint(
          '📍 Cluster layer breakdown: $clusterFeatures clusters, $individualFeatures individual pins');
      debugPrint(
          '📍 Sample feature IDs: ${features.map((f) => f['properties']['id']).take(5).toList()}...');
    } else {
      debugPrint('📍 ⚠️ No features added to cluster layer!');
    }
  }

  // ===================== NEW PIN DETECTION & ANIMATION =====================

  /// Detect truly new pins by comparing current MapProvider pins with known pins
  List<String> _detectNewPins() {
    if (_mapProvider == null) {
      debugPrint('🔍 No MapProvider available for pin detection');
      return [];
    }

    final currentPinIds = <String>{};

    debugPrint('🔍 ===== PIN DETECTION =====');
    debugPrint('🔍 MapProvider has ${_mapProvider!.pins.length} pins');

    // Get all current pin IDs from MapProvider
    for (final dynamic pin in _mapProvider!.pins) {
      String? pinId;

      if (pin is Map<String, dynamic>) {
        pinId = pin['id']?.toString();
        debugPrint('🔍 Found map pin: $pinId (Map)');
      } else {
        try {
          pinId = pin.id?.toString();
          debugPrint('🔍 Found object pin: $pinId (Object)');
        } catch (_) {
          debugPrint('🔍 Failed to get ID from pin object');
          continue;
        }
      }

      if (pinId != null && pinId.isNotEmpty) {
        currentPinIds.add(pinId);
      }
    }

    debugPrint('🔍 Current pin IDs: ${currentPinIds.toList()}');
    debugPrint('🔍 Previous pin IDs: ${_previousPinIds.toList()}');

    // Find pins that are in current but not in previous
    final newPinIds = currentPinIds.difference(_previousPinIds).toList();

    debugPrint('🔍 New pins detected: ${newPinIds.toList()}');

    // Update the previous pins set
    _previousPinIds.clear();
    _previousPinIds.addAll(currentPinIds);

    // Record timestamps for new pins
    final now = DateTime.now();
    for (final pinId in newPinIds) {
      _newPinTimestamps[pinId] = now;
      debugPrint('🔍 Recorded timestamp for new pin: $pinId');
    }

    debugPrint('🔍 ===== PIN DETECTION COMPLETE =====');

    return newPinIds;
  }

  /// Create cool animations for newly added pins
  Future<void> _animateNewPins(List<String> newPinIds) async {
    if (newPinIds.isEmpty || _mapController == null) {
      debugPrint(
          '🎉 Animation skipped - ${newPinIds.isEmpty ? 'no pins' : 'no map controller'}');
      return;
    }

    // Skip animations during initial pin load or progressive rendering
    if (_isInitialPinLoad || _isPinFetchInProgress) {
      debugPrint(
          '🎉 Skipping new pin animations during ${_isInitialPinLoad ? 'initial load' : 'progressive rendering'}');
      return;
    }

    // Only animate pins that were added from AR placement
    final arPlacedPins =
        newPinIds.where((id) => _arPlacedPinIds.contains(id)).toList();
    if (arPlacedPins.isEmpty) {
      debugPrint('🎉 No AR-placed pins to animate, skipping animations');
      return;
    }

    debugPrint(
        '🎉 Animating ${arPlacedPins.length} AR-placed pins: ${arPlacedPins.join(", ")}');

    debugPrint('🎉 ===== STARTING NEW PIN ANIMATIONS =====');
    debugPrint(
        '🎉 Animating ${arPlacedPins.length} AR-placed pins: ${arPlacedPins.join(", ")}');
    debugPrint('🎉 Available in cache: ${_pinCache.keys.toList()}');
    debugPrint('🎉 Available in realPinData: ${_realPinData.keys.toList()}');

    for (final pinId in arPlacedPins) {
      try {
        // Find pin position from cache or real pin data
        Map<String, dynamic>? pinData;

        if (_pinCache.containsKey(pinId)) {
          pinData = _pinCache[pinId];
          debugPrint('🎉 Found pin $pinId in cache');
        } else if (_realPinData.containsKey(pinId)) {
          pinData = _realPinData[pinId];
          debugPrint('🎉 Found pin $pinId in realPinData');
        } else {
          debugPrint('🎉 ❌ Pin $pinId not found in cache or realPinData!');
          // Try to find in MapProvider directly
          for (final dynamic pin in _mapProvider?.pins ?? []) {
            String? providerId;
            if (pin is Map<String, dynamic>) {
              providerId = pin['id']?.toString();
            } else {
              try {
                providerId = pin.id?.toString();
              } catch (_) {}
            }

            if (providerId == pinId) {
              if (pin is Map<String, dynamic>) {
                pinData = pin;
              } else {
                pinData = {
                  'id': pin.id,
                  'latitude': pin.latitude,
                  'longitude': pin.longitude,
                };
              }
              debugPrint('🎉 Found pin $pinId directly in MapProvider');
              break;
            }
          }
        }

        if (pinData != null) {
          final lat = pinData['latitude'] as double?;
          final lng = pinData['longitude'] as double?;

          debugPrint('🎉 Pin $pinId coordinates: lat=$lat, lng=$lng');

          if (lat != null && lng != null) {
            debugPrint('🎉 Creating animation for pin $pinId at ($lat, $lng)');
            await _createNewPinAnimation(pinId, LatLng(lat, lng));
          } else {
            debugPrint('🎉 ❌ Pin $pinId has null coordinates!');
          }
        } else {
          debugPrint('🎉 ❌ Could not find pin data for $pinId anywhere!');
        }
      } catch (e) {
        debugPrint('🎉 ❌ Error animating new pin $pinId: $e');
      }
    }

    debugPrint('🎉 ===== ANIMATION SETUP COMPLETE =====');

    // Clean up old timestamps
    _cleanupOldPinTimestamps();
  }

  /// Create a spectacular animation for a new pin
  Future<void> _createNewPinAnimation(String pinId, LatLng position) async {
    if (_mapController == null) return;

    try {
      // Add sparkle icon if not already added
      await _addSparkleIcon();

      // 1. Starburst animation - radiating sparkles
      await _createStarburstAnimation(pinId, position);

      // 2. Ripple effect - expanding rings
      await _createRippleAnimation(pinId, position);

      // 3. Pin bounce and glow - make the pin itself special
      await _createPinSpotlightEffect(pinId, position);

      debugPrint('🎉 ✨ New pin animation created for $pinId');
    } catch (e) {
      debugPrint('🎉 Error creating new pin animation: $e');
    }
  }

  /// Create beautiful progressive pin loading animation
  Future<void> _animateProgressivePins(List<Map<String, dynamic>> batch) async {
    if (_mapController == null || batch.isEmpty) return;

    try {
      debugPrint(
          '🎨 ✨ Creating progressive pin animations for ${batch.length} pins');

      // Add staggered entrance animations for each pin in the batch
      for (int i = 0; i < batch.length; i++) {
        final pin = batch[i];
        final pinId = pin['id']?.toString();
        final lat = pin['latitude'] as double?;
        final lng = pin['longitude'] as double?;

        if (pinId != null && lat != null && lng != null) {
          final position = LatLng(lat, lng);

          // Stagger the animations slightly for a wave effect
          Future.delayed(Duration(milliseconds: i * 75), () async {
            if (mounted) {
              await _createProgressivePinEntrance(pinId, position);
            }
          });
        }
      }
    } catch (e) {
      debugPrint('🎨 ❌ Error creating progressive pin animations: $e');
    }
  }

  /// Create a gentle entrance animation for progressive pin loading
  Future<void> _createProgressivePinEntrance(
      String pinId, LatLng position) async {
    if (_mapController == null) return;

    try {
      // Simple visual feedback using existing UI patterns
      debugPrint(
          '🎨 ✨ Progressive entrance animation for pin $pinId at ($position)');

      // Use existing glow animation system for progressive pins
      await _createSimpleProgressiveGlow(pinId, position);
    } catch (e) {
      debugPrint('🎨 ❌ Error creating progressive entrance for pin $pinId: $e');
    }
  }

  /// Create a simple glow effect for progressive pin loading using existing patterns
  Future<void> _createSimpleProgressiveGlow(
      String pinId, LatLng position) async {
    if (_mapController == null) return;

    try {
      // Create a temporary visual feedback using the existing pin highlight system
      debugPrint('🎨 🌟 Creating progressive glow for pin $pinId');

      // Brief highlight using existing pin bounce animation pattern
      Timer(const Duration(milliseconds: 100), () {
        if (mounted) {
          // Trigger a subtle UI update to show the pin has loaded
          setState(() {
            // This creates a subtle re-render that gives visual feedback
          });
        }
      });

      // Log the progressive loading for user feedback
      debugPrint('🎨 ✨ Pin $pinId loaded with gentle animation');
    } catch (e) {
      debugPrint('🎨 ❌ Error in simple progressive glow: $e');
    }
  }

  /// Add sparkle icon for animations
  Future<void> _addSparkleIcon() async {
    if (_mapController == null) return;

    try {
      // Check if sparkle icon already exists
      // MapLibre doesn't have a direct way to check, so we'll try adding it
      final sparkleBytes = await _createSparkleIcon();
      await _mapController!.addImage('sparkle-icon', sparkleBytes);
      debugPrint('✨ Added sparkle icon for new pin animations');
    } catch (e) {
      // Icon might already exist, that's okay
      if (!e.toString().contains('already exists')) {
        debugPrint('✨ Could not add sparkle icon: $e');
      }
    }
  }

  /// Create sparkle icon
  Future<Uint8List> _createSparkleIcon() async {
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    final size = 60.0;
    final center = Offset(size / 2, size / 2);

    // Create 4-pointed star
    final starPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 2);

    final path = Path()
      ..moveTo(center.dx, center.dy - size * 0.4) // Top
      ..lineTo(center.dx + size * 0.1, center.dy - size * 0.1) // Top-right
      ..lineTo(center.dx + size * 0.4, center.dy) // Right
      ..lineTo(center.dx + size * 0.1, center.dy + size * 0.1) // Bottom-right
      ..lineTo(center.dx, center.dy + size * 0.4) // Bottom
      ..lineTo(center.dx - size * 0.1, center.dy + size * 0.1) // Bottom-left
      ..lineTo(center.dx - size * 0.4, center.dy) // Left
      ..lineTo(center.dx - size * 0.1, center.dy - size * 0.1) // Top-left
      ..close();

    canvas.drawPath(path, starPaint);

    // Add glow effect
    final glowPaint = Paint()
      ..color = Colors.cyan.withOpacity(0.6)
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);

    canvas.drawPath(path, glowPaint);

    final picture = recorder.endRecording();
    final image = await picture.toImage(size.toInt(), size.toInt());
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    return byteData!.buffer.asUint8List();
  }

  /// Create starburst animation with sparkles radiating outward
  Future<void> _createStarburstAnimation(String pinId, LatLng position) async {
    if (_mapController == null) return;

    debugPrint('🌟 Creating VISIBLE starburst animation at $position');

    final animationController = AnimationController(
      duration:
          const Duration(milliseconds: 2000), // Longer duration for visibility
      vsync: this,
    );

    _newPinAnimationControllers.add(animationController);

    // Create sparkles at different angles using available pin icons
    final sparkleCount = 12; // More sparkles for drama
    final sparkleSymbols = <Symbol>[];

    // Try multiple icon options for sparkles
    final sparkleIcons = [
      'music-pin-common',
      'music-pin-uncommon',
      'music-pin-rare'
    ];

    for (int i = 0; i < sparkleCount; i++) {
      try {
        final iconIndex = i % sparkleIcons.length;
        final sparkleIcon = sparkleIcons[iconIndex];

        // Add sparkle symbol (start at pin position, will animate outward)
        final sparkleSymbol = await _mapController!.addSymbol(
          SymbolOptions(
            geometry: position, // Start at pin center
            iconImage: sparkleIcon, // Use available pin icons
            iconSize: 0.3, // Larger initial size for visibility
            iconAnchor: 'center',
            iconOpacity: 0.8,
          ),
        );
        sparkleSymbols.add(sparkleSymbol);
        debugPrint('🌟 Added sparkle symbol $i with icon $sparkleIcon');
      } catch (e) {
        debugPrint('✨ Could not add sparkle symbol $i: $e');
        continue;
      }
    }

    debugPrint('🌟 Created ${sparkleSymbols.length} sparkle symbols');

    if (sparkleSymbols.isEmpty) {
      debugPrint('❌ No sparkle symbols created - animation will be skipped');
      animationController.dispose();
      _newPinAnimationControllers.remove(animationController);
      return;
    }

    // Animate sparkles outward with DRAMATIC movement
    animationController.addListener(() {
      final progress = animationController.value;
      final easedProgress = Curves.easeOut.transform(progress);

      for (int i = 0; i < sparkleSymbols.length; i++) {
        final sparkleSymbol = sparkleSymbols[i];
        final angle = (i * 2 * math.pi) / sparkleSymbols.length;

        // Calculate animated position with MUCH larger movement for visibility
        final distance = easedProgress * 0.002; // 5x larger movement distance!
        final animatedLat = position.latitude + (math.cos(angle) * distance);
        final animatedLng = position.longitude + (math.sin(angle) * distance);

        // Calculate size and opacity with more dramatic changes
        final size = 0.3 + (easedProgress * 0.8); // Much larger size changes
        final opacity =
            math.max(0.1, 1.0 - easedProgress); // Keep visible longer

        try {
          _mapController!.updateSymbol(
            sparkleSymbol,
            SymbolOptions(
              geometry: LatLng(animatedLat, animatedLng),
              iconImage: 'sparkle-icon',
              iconSize: size,
              iconAnchor: 'center',
              iconOpacity: opacity,
            ),
          );
        } catch (e) {
          // Symbol might have been removed
        }
      }
    });

    // Clean up sparkles when animation completes
    animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        for (final sparkleSymbol in sparkleSymbols) {
          try {
            _mapController!.removeSymbol(sparkleSymbol);
          } catch (e) {
            // Symbol might already be removed
          }
        }
        animationController.dispose();
        _newPinAnimationControllers.remove(animationController);
      }
    });

    // Start the animation
    animationController.forward();
  }

  /// Create ripple animation with expanding circles
  Future<void> _createRippleAnimation(String pinId, LatLng position) async {
    if (_mapController == null) return;

    // Create multiple ripple layers with staggered timing
    for (int ringIndex = 0; ringIndex < 3; ringIndex++) {
      final delay = ringIndex * 50; // 50ms delay between rings (much faster)

      Timer(Duration(milliseconds: delay), () async {
        if (!mounted || _mapController == null) return;

        try {
          // Create expanding ripple with decreasing opacity
          final maxRadius = 45.0;
          final steps = 20; // Number of animation steps

          for (int step = 0; step < steps; step++) {
            if (!mounted || _mapController == null) break;

            final progress = step / (steps - 1);
            final radius = 5.0 + (progress * maxRadius);
            final opacity = math.max(0.0, 0.4 * (1.0 - progress));

            final sourceId = 'ripple-${pinId}-${ringIndex}-${step}';
            final layerId = 'ripple-layer-${pinId}-${ringIndex}-${step}';

            final circle = _createCirclePolygon(
                position.latitude, position.longitude, radius);

            // Add ripple source and layer
            await _mapController!.addSource(
              sourceId,
              GeojsonSourceProperties(
                data: {
                  'type': 'FeatureCollection',
                  'features': [
                    {
                      'type': 'Feature',
                      'geometry': circle,
                      'properties': {'id': sourceId},
                    }
                  ],
                },
              ),
            );

            await _mapController!.addFillLayer(
              sourceId,
              layerId,
              FillLayerProperties(
                fillColor: '#00FFFF', // Cyan ripples
                fillOpacity: opacity,
                fillAntialias: true,
              ),
            );

            // Remove previous step if it exists
            if (step > 0) {
              final prevSourceId = 'ripple-${pinId}-${ringIndex}-${step - 1}';
              final prevLayerId =
                  'ripple-layer-${pinId}-${ringIndex}-${step - 1}';

              _mapController!.removeLayer(prevLayerId).catchError((e) => null);
              _mapController!
                  .removeSource(prevSourceId)
                  .catchError((e) => null);
            }

            // Wait before next step
            await Future.delayed(const Duration(milliseconds: 100));
          }

          // Clean up final step
          final finalSourceId = 'ripple-${pinId}-${ringIndex}-${steps - 1}';
          final finalLayerId =
              'ripple-layer-${pinId}-${ringIndex}-${steps - 1}';

          Timer(const Duration(milliseconds: 500), () {
            _mapController?.removeLayer(finalLayerId).catchError((e) => null);
            _mapController?.removeSource(finalSourceId).catchError((e) => null);
          });
        } catch (e) {
          debugPrint('🌊 Error in ripple animation step: $e');
        }
      });
    }
  }

  /// Create spotlight effect on the actual pin
  Future<void> _createPinSpotlightEffect(String pinId, LatLng position) async {
    if (_mapController == null) return;

    // Create a pulsing spotlight effect with multiple phases
    final phases = 5; // Number of pulse phases

    for (int phase = 0; phase < phases; phase++) {
      final delay = phase * 30; // 30ms between pulses (much faster)

      Timer(Duration(milliseconds: delay), () async {
        if (!mounted || _mapController == null) return;

        try {
          final progress = phase / (phases - 1);
          final opacity = math.sin(progress * math.pi) * 0.4; // Pulse effect
          final radius = 15.0 + (progress * 5.0); // Slight size variation

          final sourceId = 'spotlight-${pinId}-${phase}';
          final layerId = 'spotlight-layer-${pinId}-${phase}';

          final glowCircle = _createCirclePolygon(
              position.latitude, position.longitude, radius);

          // Add spotlight source and layer
          await _mapController!.addSource(
            sourceId,
            GeojsonSourceProperties(
              data: {
                'type': 'FeatureCollection',
                'features': [
                  {
                    'type': 'Feature',
                    'geometry': glowCircle,
                    'properties': {'id': sourceId},
                  }
                ],
              },
            ),
          );

          await _mapController!.addFillLayer(
            sourceId,
            layerId,
            FillLayerProperties(
              fillColor: '#FFD700', // Golden spotlight
              fillOpacity: opacity,
              fillAntialias: true,
            ),
          );

          // Remove previous phase
          if (phase > 0) {
            final prevSourceId = 'spotlight-${pinId}-${phase - 1}';
            final prevLayerId = 'spotlight-layer-${pinId}-${phase - 1}';

            _mapController!.removeLayer(prevLayerId).catchError((e) => null);
            _mapController!.removeSource(prevSourceId).catchError((e) => null);
          }

          // Clean up final phase
          if (phase == phases - 1) {
            Timer(const Duration(milliseconds: 500), () {
              _mapController?.removeLayer(layerId).catchError((e) => null);
              _mapController?.removeSource(sourceId).catchError((e) => null);
            });
          }
        } catch (e) {
          debugPrint('💡 Error in spotlight phase: $e');
        }
      });
    }
  }

  /// Clean up old pin timestamps to prevent memory leaks
  void _cleanupOldPinTimestamps() {
    final now = DateTime.now();
    final cutoff = now.subtract(const Duration(minutes: 5));

    _newPinTimestamps
        .removeWhere((pinId, timestamp) => timestamp.isBefore(cutoff));
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // Handle app lifecycle changes for smart fetch manager
    _smartFetchManager.onAppLifecycleChanged(state);

    // Inform visual effects manager about app state
    final isInForeground = state == AppLifecycleState.resumed;
    _visualEffectsManager.setAppForegroundState(isInForeground);

    switch (state) {
      case AppLifecycleState.resumed:
        debugPrint('🔄 App resumed - restarting animations');
        _resumeAnimations();
        // Stop background location tracking when returning to foreground
        try {
          _mapProvider?.stopBackgroundLocationTracking();
        } catch (_) {}
        break;
      case AppLifecycleState.paused:
        debugPrint('⏸️ App paused - stopping animations');
        _pauseAnimations();
        // Start background location tracking when app goes to background
        try {
          _mapProvider?.startBackgroundLocationTracking();
        } catch (_) {}
        break;
      case AppLifecycleState.inactive:
      case AppLifecycleState.detached:
      case AppLifecycleState.hidden:
        // Also pause on these states
        _pauseAnimations();
        break;
    }
  }

  void _pauseAnimations() {
    // Cancel all animation timers
    _glowAnimationTimer?.cancel();
    _pinBounceTimer?.cancel();
    _captionUpdateTimer?.cancel();
    _playingPinGlowTimer?.cancel();
    _auraUpdateTimer?.cancel();

    // Stop animation controllers
    _pulseAnimationController.stop();
    _avatarBounceController.stop();
    _pinGlowAnimationController.stop();
    _radiusAnimationController.stop();
    _userIconAnimationController.stop();
    _pinBounceAnimationController.stop();
    _gpsFlashingController.stop();
    _playingPinAnimationController?.stop();
    _scanningAnimationController.stop();

    // Pause visual effects managers
    _visualEffectsManager.setScreenVisibility(false);

    if (_weatherEffectsManager.weatherEnabled) {
      _cachedWeatherState = true; // Remember weather was enabled
      _weatherEffectsManager
          .toggleWeatherEffects(); // This will disable weather and cancel lightning timer
    } else {
      _cachedWeatherState = false;
    }
  }

  void _resumeAnimations() {
    if (!mounted) return;

    // Restart animation controllers
    _pulseAnimationController.repeat();
    _pinGlowAnimationController.repeat(reverse: true);
    _userIconAnimationController.repeat();
    _pinBounceAnimationController.repeat(reverse: true);
    _gpsFlashingController.repeat(reverse: true);

    // Inform visual effects manager that screen is visible
    _visualEffectsManager.setScreenVisibility(true);

    // Resume scanning if it was active
    if (_isScanningActive) {
      _scanningAnimationController.repeat();
    }

    // Restart timers
    _startOptimizedAnimations();
    _updatePinsInAura();

    // Re-enable weather if it was enabled before
    if (!_weatherEffectsManager.weatherEnabled && _cachedWeatherState) {
      _weatherEffectsManager.toggleWeatherEffects();
    }
  }

  // Track weather state before pausing
  bool _cachedWeatherState = false;

  @override
  void dispose() {
    // Unsubscribe from route observer
    routeObserver.unsubscribe(this);

    // Remove WidgetsBindingObserver
    WidgetsBinding.instance.removeObserver(this);

    // Clear callbacks
    _mapProvider?.clearForceRefreshCallback();
    _mapProvider?.clearOptimisticPinCallback();

    _positionStreamSubscription?.cancel();
    _compassStreamSubscription?.cancel();
    _locationUpdateTimer?.cancel();
    _glowAnimationTimer?.cancel();
    _pinBounceTimer?.cancel();
    _captionUpdateTimer?.cancel();
    _playingPinGlowTimer?.cancel();
    _auraUpdateTimer?.cancel();
    _pinStateMonitor?.cancel();
    _sparkleCleanupTimer?.cancel();
    _scanningEffectTimer?.cancel();
    _progressiveUpdateTimer?.cancel();
    _apiCallDebounceTimer?.cancel();
    _filterChangeDebounceTimer?.cancel();

    // CRITICAL: Reset atomic flag on dispose to prevent stuck states
    _isProgressiveRenderingActive = false;
    _isStyleChangeInProgress = false;

    // Dispose isolate services
    ClusterService.instance.dispose();
    PinFeatureService.instance.dispose();

    // Dispose new pin animation controllers
    for (final controller in _newPinAnimationControllers) {
      controller.dispose();
    }
    _newPinAnimationControllers.clear();

    // Clear caches and layer state
    _clearPinCache(force: true);
    _layersInitialized = false;
    _currentlyShowingClusterLayer = false;
    // Dispose visual effects managers (handles all visual effects timers and controllers)
    _visualEffectsManager.dispose();
    _weatherEffectsManager.dispose();

    // Dispose smart fetch manager
    _smartFetchManager.dispose();
    // Dispose gesture manager
    _gestureManager.dispose();
    // Dispose layer state machine
    _layerStateMachine.dispose();
    // Dispose remaining animation controllers (non-visual effects)
    _pulseAnimationController.dispose();
    _avatarBounceController.dispose();
    _pinGlowAnimationController.dispose();
    _radiusAnimationController.dispose();
    _userIconAnimationController.dispose();
    _pinBounceAnimationController.dispose();
    _gpsFlashingController.dispose();
    _playingPinAnimationController?.dispose();
    _scanningAnimationController.dispose();
    // Dispose caption animation controllers
    for (var controller in _captionAnimationControllers.values) {
      controller.dispose();
    }
    // Remove camera position listener
    _mapController?.removeListener(_onCameraPositionChanged);
    // Remove MapProvider listener to avoid memory leaks
    _mapProvider?.removeListener(_onMapProviderUpdate);
    // Stop layer validation monitoring
    _stopLayerValidationMonitoring();
    super.dispose();
  }

  // Fetch a skin image from a network URL and add it to the map style.
  // [imageId] must be unique per pin (e.g. "pin-skin-<pinId>").
  Future<bool> _addSkinImage(
      {required String imageUrl, required String imageId}) async {
    if (_mapController == null || imageUrl.isEmpty) return false;
    try {
      // Attempt to download the image bytes
      final response = await http.get(Uri.parse(imageUrl));
      if (response.statusCode != 200) {
        debugPrint(
            'Failed to download skin image ($imageUrl) – HTTP ${response.statusCode}');
        return false;
      }

      final bytes = response.bodyBytes;

      // Decode the original image
      final codec = await ui.instantiateImageCodec(bytes);
      final frame = await codec.getNextFrame();
      final originalImage = frame.image;

      // Create a pin-shaped composition with glassmorphism effect
      final pinBytes = await _createGlassmorphismPin(originalImage);

      // Add to map
      await _mapController!.addImage(imageId, pinBytes);
      return true;
    } catch (e) {
      debugPrint('Error adding skin image: $e');
      return false;
    }
  }

  // Create a modern glassmorphism pin with the skin image
  Future<Uint8List> _createGlassmorphismPin(ui.Image skinImage) async {
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    final size = 200.0; // Larger for better quality

    // Pin dimensions
    final pinRadius = size * 0.25; // Circle radius
    final pinCenter = Offset(size * 0.5, size * 0.35);
    final stemHeight = size * 0.3;
    final stemWidth = size * 0.04; // Thinner stem (was 0.08)

    // Draw drop shadow first (subtle)
    final shadowPaint = Paint()
      ..color = Colors.black.withOpacity(0.15)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4);

    // Shadow for circle
    canvas.drawCircle(
      pinCenter.translate(1.5, 1.5),
      pinRadius + 1,
      shadowPaint,
    );

    // Shadow for stem
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromCenter(
          center: Offset(
              size * 0.5 + 1, pinCenter.dy + pinRadius + stemHeight / 2 + 1),
          width: stemWidth,
          height: stemHeight,
        ),
        const Radius.circular(4),
      ),
      shadowPaint,
    );

    // Draw pin stem with metallic gradient
    final stemRect = Rect.fromCenter(
      center: Offset(size * 0.5, pinCenter.dy + pinRadius + stemHeight / 2),
      width: stemWidth,
      height: stemHeight,
    );

    final stemGradient = LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        Colors.white.withOpacity(0.9),
        Colors.grey[300]!,
        Colors.grey[600]!,
        Colors.grey[300]!,
        Colors.white.withOpacity(0.8),
      ],
      stops: const [0.0, 0.25, 0.5, 0.75, 1.0],
    );

    canvas.drawRRect(
      RRect.fromRectAndRadius(stemRect, const Radius.circular(4)),
      Paint()..shader = stemGradient.createShader(stemRect),
    );

    // Draw stem highlight
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(
          stemRect.left + 1,
          stemRect.top,
          stemWidth * 0.3,
          stemHeight,
        ),
        const Radius.circular(2),
      ),
      Paint()..color = Colors.white.withOpacity(0.6),
    );

    // Draw subtle border for definition
    final borderPaint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2
      ..shader = LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Colors.white.withOpacity(0.6),
          Colors.white.withOpacity(0.2),
          Colors.white.withOpacity(0.4),
        ],
      ).createShader(Rect.fromCircle(center: pinCenter, radius: pinRadius));

    canvas.drawCircle(pinCenter, pinRadius, borderPaint);

    // Create circular clipping mask for the skin image
    canvas.save();
    canvas.clipPath(
      Path()
        ..addOval(Rect.fromCircle(center: pinCenter, radius: pinRadius - 4)),
    );

    // Draw the skin image with slight blur for depth
    final imageRect = Rect.fromCircle(center: pinCenter, radius: pinRadius - 4);
    final imagePaint = Paint()..filterQuality = FilterQuality.high;

    canvas.drawImageRect(
      skinImage,
      Rect.fromLTWH(
          0, 0, skinImage.width.toDouble(), skinImage.height.toDouble()),
      imageRect,
      imagePaint,
    );

    canvas.restore();

    // Convert to image
    final picture = recorder.endRecording();
    final image = await picture.toImage(size.toInt(), size.toInt());
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    return byteData!.buffer.asUint8List();
  }

  // Add only the cluster icons we actually need (optimized)
  Future<void> _addNeededClusterIcons(Set<int> neededCounts) async {
    if (_mapController == null || neededCounts.isEmpty) return;

    try {
      final List<Future> iconTasks = [];

      // Only create icons for counts we need and haven't created yet
      for (final count in neededCounts) {
        if (!_createdClusterIcons.contains(count)) {
          iconTasks.add(_createAndAddClusterIcon(count));
        }
      }

      // Create icons concurrently for better performance
      if (iconTasks.isNotEmpty) {
        await Future.wait(iconTasks);
        debugPrint(
            '🎯 Added ${iconTasks.length} new cluster icons: ${neededCounts.difference(_createdClusterIcons)}');
      }
    } catch (e) {
      debugPrint('Error adding cluster icons: $e');
    }
  }

  // Create and add a single cluster icon
  Future<void> _createAndAddClusterIcon(int count) async {
    if (_mapController == null) return;

    // Cap cluster count display at 100 to prevent excessive icon creation
    final displayCount = count > 100 ? 100 : count;

    try {
      final iconBytes = await _createClusterIcon(displayCount);
      await _mapController!.addImage('cluster-icon-$count', iconBytes);
      _createdClusterIcons.add(count);
    } catch (e) {
      debugPrint('Error creating cluster icon for count $count: $e');
    }
  }

  // Create cluster icon with glassmorphism design and count
  Future<Uint8List> _createClusterIcon(int count) async {
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    final size = 160.0; // Larger to accommodate stem
    final pinCenter =
        Offset(size / 2, size * 0.35); // Move pin head up for stem
    final radius = size * 0.25;
    final stemHeight = size * 0.3;
    final stemWidth = size * 0.08;

    // Draw drop shadow for entire pin (head + stem, subtle)
    final shadowPaint = Paint()
      ..color = Colors.black.withOpacity(0.15)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4);

    // Shadow for pin head
    canvas.drawCircle(pinCenter.translate(1.5, 1.5), radius + 1, shadowPaint);

    // Shadow for stem
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromCenter(
          center: Offset(
              size * 0.5 + 1, pinCenter.dy + radius + stemHeight / 2 + 1),
          width: stemWidth,
          height: stemHeight,
        ),
        const Radius.circular(4),
      ),
      shadowPaint,
    );

    // Draw pin stem with metallic gradient
    final stemRect = Rect.fromCenter(
      center: Offset(size * 0.5, pinCenter.dy + radius + stemHeight / 2),
      width: stemWidth,
      height: stemHeight,
    );

    final stemGradient = LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        Colors.white.withOpacity(0.9),
        Colors.grey[300]!,
        Colors.grey[600]!,
        Colors.grey[300]!,
        Colors.white.withOpacity(0.8),
      ],
      stops: const [0.0, 0.25, 0.5, 0.75, 1.0],
    );

    canvas.drawRRect(
      RRect.fromRectAndRadius(stemRect, const Radius.circular(4)),
      Paint()..shader = stemGradient.createShader(stemRect),
    );

    // Draw stem highlight
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(
          stemRect.left + 1,
          stemRect.top,
          stemWidth * 0.3,
          stemHeight,
        ),
        const Radius.circular(2),
      ),
      Paint()..color = Colors.white.withOpacity(0.6),
    );

    // Create glassmorphism background
    final glassPaint = Paint()
      ..shader = RadialGradient(
        center: const Alignment(-0.3, -0.3),
        radius: 1.2,
        colors: [
          Colors.white.withOpacity(0.4),
          Colors.white.withOpacity(0.25),
          Colors.white.withOpacity(0.15),
          Colors.blue.withOpacity(0.1),
        ],
        stops: const [0.0, 0.4, 0.7, 1.0],
      ).createShader(Rect.fromCircle(center: pinCenter, radius: radius));

    // Draw glass background circle
    canvas.drawCircle(pinCenter, radius, glassPaint);

    // Add subtle color overlay based on cluster size
    Color clusterColor = count > 10
        ? const Color(0xFFE91E63)
        : // Pink for large clusters
        count > 5
            ? const Color(0xFF00E5FF)
            : // Blue for medium clusters
            const Color(0xFF4CAF50); // Green for small clusters

    final colorOverlayPaint = Paint()..color = clusterColor.withOpacity(0.2);
    canvas.drawCircle(pinCenter, radius, colorOverlayPaint);

    // Draw gradient border with glassmorphism effect
    final borderPaint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.5
      ..shader = LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Colors.white.withOpacity(0.8),
          Colors.white.withOpacity(0.3),
          Colors.white.withOpacity(0.6),
        ],
      ).createShader(Rect.fromCircle(center: pinCenter, radius: radius));

    canvas.drawCircle(pinCenter, radius, borderPaint);

    // Add inner highlight for glassmorphism effect
    final highlightPaint = Paint()
      ..shader = RadialGradient(
        center: const Alignment(-0.4, -0.4),
        radius: 0.6,
        colors: [
          Colors.white.withOpacity(0.6),
          Colors.white.withOpacity(0.2),
          Colors.transparent,
        ],
      ).createShader(Rect.fromCircle(center: pinCenter, radius: radius * 0.8));

    canvas.drawCircle(pinCenter, radius * 0.8, highlightPaint);

    // Add outer glow
    final glowPaint = Paint()
      ..color = clusterColor.withOpacity(0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 6);

    canvas.drawCircle(pinCenter, radius + 3, glowPaint);

    // Draw count text with shadow
    final textShadowPainter = TextPainter(
      text: TextSpan(
        text: count.toString(),
        style: TextStyle(
          color: Colors.black.withOpacity(0.3),
          fontSize: count > 99
              ? 18
              : count > 9
                  ? 22
                  : 26,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    textShadowPainter.layout();
    textShadowPainter.paint(
      canvas,
      Offset(
        (size - textShadowPainter.width) / 2 + 1,
        (pinCenter.dy - textShadowPainter.height / 2) +
            1, // Center on pin head, not entire image
      ),
    );

    // Draw main count text
    final textPainter = TextPainter(
      text: TextSpan(
        text: count.toString(),
        style: TextStyle(
          color: Colors.white,
          fontSize: count > 99
              ? 18
              : count > 9
                  ? 22
                  : 26,
          fontWeight: FontWeight.bold,
          shadows: [
            Shadow(
              color: Colors.black.withOpacity(0.5),
              offset: const Offset(1, 1),
              blurRadius: 2,
            ),
          ],
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        (size - textPainter.width) / 2,
        pinCenter.dy -
            textPainter.height / 2, // Center on pin head, not entire image
      ),
    );

    // Add subtle sparkles for clusters with many pins
    if (count > 5) {
      _drawClusterSparkles(canvas, pinCenter, radius);
    }

    // Convert to image
    final picture = recorder.endRecording();
    final image = await picture.toImage(size.toInt(), size.toInt());
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    return byteData!.buffer.asUint8List();
  }

  // Draw sparkles for cluster icons
  void _drawClusterSparkles(Canvas canvas, Offset center, double radius) {
    final sparklePaint = Paint()
      ..color = Colors.white.withOpacity(0.6)
      ..style = PaintingStyle.fill;

    // Small sparkles around the cluster
    final sparklePositions = [
      Offset(center.dx - radius * 0.8, center.dy - radius * 0.6),
      Offset(center.dx + radius * 0.7, center.dy - radius * 0.7),
      Offset(center.dx + radius * 0.9, center.dy + radius * 0.4),
    ];

    for (int i = 0; i < sparklePositions.length; i++) {
      final pos = sparklePositions[i];
      final sparkleSize = 2.0 + i * 0.5;

      // Draw 4-pointed star sparkle
      final path = Path()
        ..moveTo(pos.dx, pos.dy - sparkleSize)
        ..lineTo(pos.dx + sparkleSize * 0.3, pos.dy - sparkleSize * 0.3)
        ..lineTo(pos.dx + sparkleSize, pos.dy)
        ..lineTo(pos.dx + sparkleSize * 0.3, pos.dy + sparkleSize * 0.3)
        ..lineTo(pos.dx, pos.dy + sparkleSize)
        ..lineTo(pos.dx - sparkleSize * 0.3, pos.dy + sparkleSize * 0.3)
        ..lineTo(pos.dx - sparkleSize, pos.dy)
        ..lineTo(pos.dx - sparkleSize * 0.3, pos.dy - sparkleSize * 0.3)
        ..close();

      canvas.drawPath(path, sparklePaint);
    }
  }

  // Get pin color based on rarity
  Color _getPinColor(String rarity) {
    switch (rarity) {
      case 'legendary':
        return const Color(0xFFFFE55C);
      case 'epic':
        return const Color(0xFFE91E63);
      case 'rare':
        return const Color(0xFF00E5FF);
      case 'uncommon':
        return const Color(0xFF4CAF50);
      default:
        return const Color(0xFF2196F3);
    }
  }

  // Get upvotes count from pin data
  int _getPinUpvotes(Map<String, dynamic> pinData) {
    final upvoteData = pinData['upvote_count'];
    if (upvoteData is int) {
      return upvoteData;
    } else if (upvoteData is String) {
      return int.tryParse(upvoteData) ?? 0;
    } else {
      return 0;
    }
  }

  // Get downvotes count from pin data
  int _getPinDownvotes(Map<String, dynamic> pinData) {
    final downvoteData = pinData['downvote_count'];
    if (downvoteData is int) {
      return downvoteData;
    } else if (downvoteData is String) {
      return int.tryParse(downvoteData) ?? 0;
    } else {
      return 0;
    }
  }

  // Build map control button helper method
  Widget _buildMapControlButton(
    BuildContext context, {
    required IconData icon,
    required String tooltip,
    required VoidCallback onPressed,
    Color? backgroundColor,
    Color? foregroundColor,
    double iconSize = 20,
    bool isTransparent = false,
  }) {
    return Container(
      width: 44,
      height: 44,
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(25),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: isTransparent
            ? null
            : [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(25),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(25),
            ),
            child: Icon(
              icon,
              color: foregroundColor ?? Colors.black87,
              size: iconSize,
            ),
          ),
        ),
      ),
    );
  }

  // Build filter button with morphing dropdown
  Widget _buildFilterButton(bool isDarkMode) {
    return AnimatedBuilder(
      animation: _filterAnimation,
      builder: (context, child) {
        final themeProvider =
            Provider.of<ThemeProvider>(context, listen: false);

        // Morph from button to dropdown based on animation
        if (!_isFilterDropdownOpen) {
          // Regular button state
          return Column(
            children: [
              Container(
                width: 44,
                height: 44,
                decoration: BoxDecoration(
                  color: isDarkMode
                      ? Colors.black.withOpacity(0.8)
                      : Colors.white.withOpacity(0.9),
                  borderRadius: BorderRadius.circular(25),
                  border: Border.all(
                    color: _getFilterColor().withOpacity(0.5),
                    width: 2,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: _toggleFilterDropdown,
                    borderRadius: BorderRadius.circular(25),
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(25),
                        gradient: RadialGradient(
                          colors: [
                            _getFilterColor().withOpacity(0.2),
                            Colors.transparent,
                          ],
                        ),
                      ),
                      child: Icon(
                        _getFilterIcon(),
                        color: _getFilterColor(),
                        size: 20,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: isDarkMode
                      ? Colors.black.withOpacity(0.8)
                      : Colors.white.withOpacity(0.9),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Text(
                  _getFilterName(),
                  style: TextStyle(
                    color: isDarkMode ? Colors.white : Colors.black87,
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          );
        } else {
          // Dropdown state
          return Container(
            width: 120 * _filterAnimation.value,
            constraints: const BoxConstraints(minWidth: 44),
            decoration: BoxDecoration(
              color: isDarkMode
                  ? Colors.black.withOpacity(0.85)
                  : Colors.white.withOpacity(0.95),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: themeProvider.primaryColor.withOpacity(0.3),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: _filterAnimation.value > 0.5
                ? _buildFilterDropdownContent(isDarkMode)
                : const SizedBox.shrink(),
          );
        }
      },
    );
  }

  // Build filter dropdown content
  Widget _buildFilterDropdownContent(bool isDarkMode) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Header with close button
        Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Filter',
                style: TextStyle(
                  color: isDarkMode ? Colors.white : Colors.black87,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              GestureDetector(
                onTap: _toggleFilterDropdown,
                child: Icon(
                  Icons.close,
                  color: isDarkMode ? Colors.white70 : Colors.black54,
                  size: 18,
                ),
              ),
            ],
          ),
        ),

        // Filter options
        _buildFilterOption(PinFilterType.all, Icons.public, 'All', isDarkMode),
        _buildFilterOption(
            PinFilterType.fresh, Icons.fiber_new, 'Fresh', isDarkMode),
        _buildFilterOption(
            PinFilterType.friends, Icons.people, 'Friends', isDarkMode),

        const SizedBox(height: 8),
      ],
    );
  }

  // Build individual filter option
  Widget _buildFilterOption(
      PinFilterType filterType, IconData icon, String label, bool isDarkMode) {
    final isSelected = _currentFilter == filterType;
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _selectFilter(filterType),
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: isSelected
                  ? themeProvider.primaryColor.withOpacity(0.2)
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(12),
              border: isSelected
                  ? Border.all(
                      color: themeProvider.primaryColor.withOpacity(0.4),
                      width: 1,
                    )
                  : null,
            ),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: isSelected
                      ? themeProvider.primaryColor
                      : (isDarkMode ? Colors.white70 : Colors.black54),
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    label,
                    style: TextStyle(
                      color: isSelected
                          ? themeProvider.primaryColor
                          : (isDarkMode ? Colors.white : Colors.black87),
                      fontSize: 12,
                      fontWeight:
                          isSelected ? FontWeight.w600 : FontWeight.w500,
                    ),
                  ),
                ),
                if (isSelected)
                  Icon(
                    Icons.check_circle,
                    color: themeProvider.primaryColor,
                    size: 14,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Toggle filter dropdown
  void _toggleFilterDropdown() {
    setState(() {
      _isFilterDropdownOpen = !_isFilterDropdownOpen;
    });

    if (_isFilterDropdownOpen) {
      _filterAnimationController.forward();
    } else {
      _filterAnimationController.reverse();
    }

    // Haptic feedback
    HapticFeedback.lightImpact();
  }

  // Select filter and apply
  void _selectFilter(PinFilterType filterType) {
    if (_currentFilter == filterType) return;

    setState(() {
      _currentFilter = filterType;
    });

    // Update MapProvider with new filter
    if (_mapProvider != null) {
      _mapProvider!.setCurrentFilter(filterType);
    }

    // Close dropdown
    _toggleFilterDropdown();

    // Apply filter
    _applyPinFilter();

    // Haptic feedback
    HapticFeedback.mediumImpact();
  }

  // Apply pin filter by fetching different data
  void _applyPinFilter() async {
    if (_mapController == null || _currentPosition == null) return;

    // Cancel any existing filter change debounce timer
    _filterChangeDebounceTimer?.cancel();

    // Debounce filter changes to prevent multiple simultaneous API calls
    _filterChangeDebounceTimer = Timer(_filterChangeDebounceDelay, () async {
      await _performFilterChange();
    });
  }

  // Perform the actual filter change (separated for debouncing)
  Future<void> _performFilterChange() async {
    if (_mapController == null || _currentPosition == null) return;

    // Cancel any ongoing progressive rendering to prevent conflicts
    _progressiveUpdateTimer?.cancel();

    // Reset progressive rendering state completely for filter changes
    _isPinFetchInProgress = false;
    _pendingPins.clear();
    _renderedPinIds.clear();

    // Stop any scanning animation
    // _stopScanningAnimation();

    setState(() {
      _isLoadingPins = true;
    });

    try {
      // --- NEW: Force clear all caches and layer state ---
      _pinCache.clear();
      _clusterCache.clear();
      _layersInitialized = false;
      _allPinsData.clear();
      _realPinData.clear();

      // Clear layer features tracking to ensure clean state
      _clearLayerFeatures();

      // Set rebuild flag for filter changes (full layer rebuild required)
      _needsLayerRebuild = true;

      // Clear created cluster icons to force recreation
      _createdClusterIcons.clear();

      // Clear existing pins from map before fetching new ones
      await _clearExistingSymbols();
      await _removeGlowLayers();

      // Use smart fetch manager instead of direct API calls
      debugPrint('📍 Filter change - using smart fetch manager');

      // Update the smart fetch manager with the new filter BEFORE fetching
      _smartFetchManager.setFilter(_currentFilter);
      debugPrint('📍 SmartFetch: Updated filter to $_currentFilter');

      // Force refresh with smart fetch manager for filter changes
      await _smartFetchManager.forceRefresh(_currentPosition!);

      // Wait a moment for smart fetch manager to complete and populate cache
      debugPrint('📍 Waiting for smart fetch manager to complete...');
      await Future.delayed(const Duration(milliseconds: 500));

      // Get pins from cache after smart fetch manager has had time to update it
      final initialPinMaps = _pinCache.values.toList();

      // If still empty after initial wait, wait a bit longer for progressive rendering
      if (initialPinMaps.isEmpty) {
        debugPrint(
            '📍 No pins found immediately, waiting for progressive rendering...');
        await Future.delayed(const Duration(
            milliseconds: 1500)); // Additional wait for progressive rendering

        // Check again after giving progressive rendering time to complete
        final finalPinMaps = _pinCache.values.toList();

        // Only show "no pins found" if truly no pins after reasonable wait time
        if (finalPinMaps.isEmpty) {
          // Clear all layers to show blank map
          await _clearExistingSymbols();
          await _removeGlowLayers();

          // Explicitly hide any remaining layers to ensure blank map
          if (_mapController != null) {
            try {
              await _mapController!
                  .setLayerVisibility('individual-pins-layer', false);
              await _mapController!
                  .setLayerVisibility('cluster-pins-layer', false);
              await _mapController!
                  .setLayerVisibility('individual-glow-fill-0', false);
              await _mapController!
                  .setLayerVisibility('individual-glow-fill-1', false);
              await _mapController!
                  .setLayerVisibility('individual-aura-border-layer', false);
              await _mapController!
                  .setLayerVisibility('individual-glow-pulse-layer', false);
              await _mapController!
                  .setLayerVisibility('cluster-glow-fill-0', false);
              await _mapController!
                  .setLayerVisibility('cluster-glow-fill-1', false);
              await _mapController!
                  .setLayerVisibility('cluster-aura-border-layer', false);
              await _mapController!
                  .setLayerVisibility('cluster-glow-pulse-layer', false);
            } catch (e) {
              debugPrint('📍 Error hiding layers: $e');
            }
          }

          // Clear data
          _allPinsData.clear();
          _realPinData.clear();
          _pinCache.clear();
          _clusterCache.clear();

          // Don't sync with provider pins during filter changes - keep filtered cache empty
          debugPrint('🔍 Keeping cache empty for filter - not syncing with provider pins');

          debugPrint(
              '🔍 No ${_currentFilter.toString().split('.').last} pins found after waiting - cleared map');
              _showNoPinsFoundSnackbar();
          return;
        } else {
          debugPrint(
              '📍 Found ${finalPinMaps.length} pins after progressive rendering completed');
        }
      } else {
        debugPrint(
            '📍 Found ${initialPinMaps.length} pins immediately from cache');
      }

      // Use the latest pin data from cache
      final pinMaps = _pinCache.values.toList();

      // Use the cached pin maps directly (they're already in the correct format)
      _allPinsData = pinMaps;

      // Add visual enhancements based on filter type
      for (final pinMap in _allPinsData) {
        if (_currentFilter == PinFilterType.fresh) {
          pinMap['glow_intensity'] = 1.5;
          pinMap['visual_effect'] = 'glow';
        } else if (_currentFilter == PinFilterType.friends) {
          pinMap['scale'] = 1.3;
          pinMap['visual_effect'] = 'pulse';
        }

        // Ensure proper rarity calculation
        pinMap['rarity'] = _getPinRarity(pinMap);

        // Mark as from filter
        pinMap['from_filter'] = true;
      }

      // --- FULL RELOAD: clear and rebuild all layers ---
      await _clearExistingSymbols(); // Remove all pin/cluster symbols
      await _removeGlowLayers(); // Remove all glow overlays

      // Sync filtered pins into cache for visual effects
      _syncFilteredPinsToCache(_allPinsData);

      // Always use progressive rendering (no legacy batch rendering)
      debugPrint(
          '📍 Using progressive rendering for filter with ${_allPinsData.length} pins');

      // Initialize empty layers
      await _initializeEmptyLayers();

      // Ensure progressive rendering state is completely reset
      _isPinFetchInProgress = false;
      _pendingPins.clear();
      _renderedPinIds.clear();
      _pendingPins.addAll(_allPinsData);

      // Set rendering context for filter change
      _currentRenderingContext = RenderingContext.filterChange;
      debugPrint('📍 🔄 Set rendering context to filterChange for progressive rendering');

      // Start scanning animation for visual feedback
      _startScanningAnimation();

      // Start progressive rendering with a small delay to ensure state is clean
      Timer(const Duration(milliseconds: 100), () {
        if (mounted && !_isPinFetchInProgress) {
          _startProgressiveRendering();
        }
      });

      // Ensure layers are marked as initialized after filter application
      _layersInitialized = true;

      // Force layers to be visible after filter change (in case they were hidden when no pins found)
      if (_mapController != null && _allPinsData.isNotEmpty) {
        try {
          debugPrint('📍 🔄 Forcing layers visible after successful filter change with ${_allPinsData.length} pins');
          // Ensure at least one layer is visible - we'll let _switchLayerVisibility handle the correct one
          await _mapController!.setLayerVisibility('individual-pins-layer', true);
          await _mapController!.setLayerVisibility('cluster-pins-layer', true);
        } catch (e) {
          debugPrint('📍 ⚠️ Error forcing layer visibility: $e');
        }
      }

      // Ensure correct layer visibility based on current zoom
      final shouldCluster = _currentZoom < _maxZoomForClustering;
      await _switchLayerVisibility(shouldCluster);
      _currentlyShowingClusterLayer = shouldCluster;
      _isCurrentlyShowingClusters = shouldCluster;

      // Restore playing pin state if there was one
      if (_currentlyPlayingPinId != null) {
        debugPrint('🎵 Restoring playing pin state: $_currentlyPlayingPinId');
        _updatePinLayersForPlayingState();
        _startPlayingPinGlowAnimation();
      }

      // Update pins in aura with new filtered data
      _updatePinsInAura();

      // Update caption popups for visible pins
      if (_mapController != null) {
        final cameraPosition = _mapController!.cameraPosition;
        if (cameraPosition != null) {
          _updateCaptionPopups(_currentZoom, cameraPosition);
        }
      }

      debugPrint(
          '🔍 Applied ${_currentFilter.toString().split('.').last} filter: ${_allPinsData.length} pins found');
    } catch (e, st) {
      debugPrint('❌ Error applying pin filter: $e\n$st');
      // Reset progressive rendering state on error
      _isPinFetchInProgress = false;
      _progressiveUpdateTimer?.cancel();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('Failed to load ${_getFilterName().toLowerCase()} pins'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      // Reset rendering context after filter change is complete
      _currentRenderingContext = RenderingContext.incrementalUpdate;
      debugPrint('📍 🔄 Reset rendering context to incrementalUpdate after filter change');

      if (mounted) {
        setState(() {
          _isLoadingPins = false;
        });
      }
    }
  }

  // Get current filter icon
  IconData _getFilterIcon() {
    switch (_currentFilter) {
      case PinFilterType.all:
        return Icons.public;
      case PinFilterType.fresh:
        return Icons.fiber_new;
      case PinFilterType.friends:
        return Icons.people;
    }
  }

  // Get current filter color
  Color _getFilterColor() {
    switch (_currentFilter) {
      case PinFilterType.all:
        return Colors.blue;
      case PinFilterType.fresh:
        return Colors.green;
      case PinFilterType.friends:
        return Colors.orange;
    }
  }

  // Get current filter name
  String _getFilterName() {
    switch (_currentFilter) {
      case PinFilterType.all:
        return 'All';
      case PinFilterType.fresh:
        return 'Fresh';
      case PinFilterType.friends:
        return 'Friends';
    }
  }

  // Helper method to show "no pins found" snackbar
  void _showNoPinsFoundSnackbar() {
    if (mounted && !_isLoadingPins) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('No ${_getFilterName().toLowerCase()} pins found in this area'),
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  // Handle camera movement to track zoom level
  void _onCameraMove(CameraPosition position) {
    final previousZoom = _currentZoom;
    _currentZoom = position.zoom;

    // Check if we crossed the zoom threshold for immediate layer switching
    if (_layersInitialized && !_isSwitchingLayers) {
      final previousShouldCluster = previousZoom < _maxZoomForClustering;
      final currentShouldCluster = _currentZoom < _maxZoomForClustering;

      // If we crossed the threshold, switch layers immediately
      if (previousShouldCluster != currentShouldCluster) {
        debugPrint(
            '📍 ⚡ Zoom threshold crossed: ${previousZoom.toStringAsFixed(1)} → ${_currentZoom.toStringAsFixed(1)}, switching to ${currentShouldCluster ? 'clusters' : 'individual pins'}');

        // Update layer state machine for immediate transition
        final targetState = currentShouldCluster
            ? LayerState.showingClusters
            : LayerState.showingIndividual;

        // Switch layers immediately without waiting for camera idle
        _switchLayerVisibility(currentShouldCluster).then((_) {
          _currentlyShowingClusterLayer = currentShouldCluster;
          _isCurrentlyShowingClusters = currentShouldCluster;

          // Update state machine to reflect the immediate change
          _layerStateMachine.transitionTo(targetState, context: {
            'zoom': _currentZoom,
            'trigger': 'immediate_zoom_threshold',
            'previous_zoom': previousZoom,
          });
        }).catchError((e) {
          debugPrint('📍 ⚠️ Immediate layer switch failed: $e');
        });
      }
    }
  }

  // Handle camera idle with smart rebuild logic
  void _onCameraIdle() async {
    if (!_isMapReady) return;

    debugPrint('📷 Camera idle at zoom: $_currentZoom');

    // Only rebuild layers if explicitly needed, not on zoom operations
    if (_needsLayerRebuild) {
      debugPrint('📍 Layer rebuild needed - rebuilding pins/clusters');
      await _updateMapPins();
      _needsLayerRebuild = false;
    } else {
      debugPrint('📍 Zoom-only operation - skipping layer rebuild');
      // For zoom operations, just ensure correct layer visibility
      final shouldCluster = _currentZoom < _maxZoomForClustering;
      final currentlyShowingClusters = _currentlyShowingClusterLayer;

      if (shouldCluster != currentlyShowingClusters &&
          !_isSwitchingLayers &&
          _layersInitialized) {
        debugPrint(
            '📍 🔄 Camera idle: switching layer visibility only (zoom=$_currentZoom, shouldCluster=$shouldCluster)');
        try {
          await _switchLayerVisibility(shouldCluster);
          _currentlyShowingClusterLayer = shouldCluster;
          _isCurrentlyShowingClusters = shouldCluster;
        } catch (e) {
          debugPrint('📍 ⚠️ Layer visibility switch failed: $e');
        }
      }
    }
  }

  // Handle camera position changes from MaplibreMapController
  void _onCameraPositionChanged() async {
    if (_mapController == null || !_isMapReady) return;

    final cameraPosition = _mapController!.cameraPosition;
    if (cameraPosition != null) {
      final newZoom = cameraPosition.zoom;
      final newBearing = cameraPosition.bearing ?? 0.0;

      // Store old zoom for comparison
      final oldZoom = _currentZoom;
      // Always update current zoom to ensure clustering decisions are correct
      _currentZoom = newZoom;

      // Cancel previous debounce timer
      _cameraPositionDebounceTimer?.cancel();

      // Debounce camera position changes to prevent excessive triggering
      _cameraPositionDebounceTimer = Timer(_cameraPositionDebounceDelay, () {
        _handleDebouncedCameraChange(newZoom, oldZoom, newBearing);
      });
    }
  }

  // Handle debounced camera position changes
  void _handleDebouncedCameraChange(
      double newZoom, double oldZoom, double newBearing) async {
    if (_mapController == null || !_isMapReady) return;

    // Check if we need to switch between cluster and individual views
    final shouldCluster = newZoom < _maxZoomForClustering;
    final currentlyShowingClusters = _currentlyShowingClusterLayer;

    // CRITICAL FIX: Don't switch layers during progressive rendering
    // This prevents pins from disappearing when user zooms out during progressive rendering
    if (_isProgressiveRenderingActive || _isPinFetchInProgress || _progressiveUpdateTimer?.isActive == true) {
      debugPrint('📍 ⚠️ Progressive rendering active - deferring layer switch until completion');
      debugPrint('📍 ⚠️ Current state: shouldCluster=$shouldCluster, currentlyShowingClusters=$currentlyShowingClusters');
      debugPrint('📍 ⚠️ Debug state: _isProgressiveRenderingActive=$_isProgressiveRenderingActive, _isPinFetchInProgress=$_isPinFetchInProgress, timerActive=${_progressiveUpdateTimer?.isActive}, pendingPins=${_pendingPins.length}');
      
      // Set a flag to switch layers after progressive rendering completes
      if (shouldCluster != currentlyShowingClusters) {
        _pendingClusterDisplay = shouldCluster;
        debugPrint('📍 ⚠️ Deferred layer switch: will switch to ${shouldCluster ? 'clusters' : 'individual pins'} after progressive rendering');
      }
      return;
    }

    // Always attempt the visibility switch when the desired clustering state
    // differs from what is currently shown.  Previously we skipped this when
    // `_layersInitialized` was false which could happen right after a pin is
    // added (e.g. from the AR screen) because layers are recreated
    // asynchronously – resulting in the map staying in cluster mode even
    // after the user zoomed in.  Removing that guard guarantees we update
    // the layer visibility as soon as a zoom change is detected.
    if (shouldCluster != currentlyShowingClusters && !_isSwitchingLayers) {
      debugPrint(
          '📍 🔄 Zoom-triggered layer switch: zoom=$newZoom, shouldCluster=$shouldCluster, currently showing clusters=$currentlyShowingClusters, layers initialized=$_layersInitialized');

      // If switching to cluster view and we have progressively rendered pins,
      // ensure the cluster layer is properly populated
      // if (shouldCluster && _renderedPinIds.isNotEmpty && _layersInitialized) {
      //   debugPrint(
      //       '📍 🔄 Switching to cluster view with ${_renderedPinIds.length} progressively rendered pins');

      //   // Check if we have cached clusters for this zoom level first
      //   final cacheKey = 'zoom_${newZoom.toStringAsFixed(1)}';
      //   if (_clusterCache.containsKey(cacheKey)) {
      //     debugPrint('📍 🔄 Using cached clusters for zoom level $newZoom');
      //     try {
      //       final cachedClusters = _clusterCache[cacheKey]!;
      //       await _updateClusterLayerFeatures(cachedClusters,
      //           forceFullUpdate: true);
      //       _updateClusterSymbolsFromFeatures(cachedClusters);
      //     } catch (e) {
      //       debugPrint('📍 ⚠️ Error using cached clusters: $e');
      //       // Fallback to rebuilding if cache fails
      //       await _rebuildClusterLayerWithAllPinsOptimized();
      //     }
      //   } else {
      //     // Only rebuild if we don't have cached clusters
      //     await _rebuildClusterLayerWithAllPinsOptimized();
      //   }
      // }

      // Try to switch layer visibility using enhanced synchronization
      try {
        await _synchronizeLayerVisibility(shouldCluster, 'camera_change');
        _currentlyShowingClusterLayer = shouldCluster;
        _isCurrentlyShowingClusters = shouldCluster;
        debugPrint('📍 ✅ Layer synchronization completed successfully');

        // Immediately validate the layer state after switching
        Timer(const Duration(milliseconds: 100), () {
          if (mounted) {
            _layerStateMachine.validateAndCorrect();
          }
        });
      } catch (e) {
        debugPrint('📍 ⚠️ Layer synchronization failed (likely timing issue): $e');

        // Emergency recovery: ensure pin layer visibility matches glow layers
        await _emergencyPinLayerRecovery();

        // If layers aren't ready yet, trigger a rebuild to ensure proper state
        if (!_layersInitialized) {
          debugPrint('📍 🔄 Layers not initialized, will trigger rebuild');
          // Timer(const Duration(milliseconds: 500), () {
          //   if (mounted && _isMapReady && _pinCache.isNotEmpty) {
          //     _updateMapPinsFromCache();
          //   }
          // });
        } else {
          // Force immediate validation if layers are initialized but switching failed
          Timer(const Duration(milliseconds: 200), () {
            if (mounted) {
              _layerStateMachine.validateAndCorrect();
            }
          });
        }
      }
    }

    // Only update from cache if zoom level changed significantly AND we don't have progressive rendering active
    if ((newZoom - oldZoom).abs() > 1.5 &&
        !_isPinFetchInProgress &&
        _pendingPins.isEmpty) {
      debugPrint(
          '📍 🔄 Major zoom change: $oldZoom → $newZoom, checking if cache update needed');

      // Check if most pins are already rendered
      final totalCachedPins = _pinCache.length;
      final renderedPins = _renderedPinIds.length;

      if (renderedPins < totalCachedPins * 0.7) {
        debugPrint(
            '📍 🔄 Only ${renderedPins}/${totalCachedPins} pins rendered, updating from cache');
        // Update from cache without triggering new API calls
        Timer(const Duration(milliseconds: 800), () {
          if (mounted && _isMapReady && _pinCache.isNotEmpty) {
            _updateMapPinsFromCache().then((_) {
              // Validate pins after camera-triggered update
              _validatePinState();
            });
          }
        });
      } else {
        debugPrint(
            '📍 🔄 Most pins already rendered (${renderedPins}/${totalCachedPins}), skipping cache update');
      }
    }

    // Only update compass rotation when map bearing changes significantly (increased threshold)
    if ((newBearing - _currentMapBearing).abs() > 10.0 &&
        _currentHeading != null &&
        _userLocationSymbol != null) {
      _currentMapBearing = newBearing;
      _updateUserMarkerRotation(_currentHeading!);
    }

    // Update caption popups based on zoom level and visible pins (less frequently)
    if ((newZoom - (_lastCaptionZoom)).abs() > 0.5) {
      final cameraPosition = _mapController!.cameraPosition;
      if (cameraPosition != null) {
        _updateCaptionPopups(newZoom, cameraPosition);
      }
    }
  }

  // Automatically tilt the map to reveal 3D buildings
  void _autoTiltForBuildings() {
    if (_mapController == null || !_isMapReady) return;

    // Only auto-tilt if 3D buildings are enabled
    if (widget.show3DBuildings != true) return;

    // Check if current zoom level supports 3D buildings
    if (_currentZoom < _minZoomFor3D) return;

    debugPrint(
        '🏢 Auto-tilting map to reveal 3D buildings (zoom: $_currentZoom)');

    // Animate to the default tilt angle to show 3D buildings
    _mapController!.animateCamera(
      CameraUpdate.tiltTo(_defaultTilt),
      duration: const Duration(milliseconds: 1000), // Smooth 1-second animation
    );
  }

  // Track last zoom level and position to prevent unnecessary updates
  double _lastCaptionZoom = -1;
  LatLng? _lastCaptionPosition;

  // Update caption popups visibility based on zoom level and visible pins
  void _updateCaptionPopups(double zoom, CameraPosition cameraPosition) {
    // Check if zoom or position changed significantly to prevent excessive calls
    final zoomChanged = (_lastCaptionZoom - zoom).abs() >= 0.1;
    final positionChanged = _lastCaptionPosition == null ||
        ((_lastCaptionPosition!.latitude - cameraPosition.target.latitude)
                    .abs() >
            0.0001 ||
        (_lastCaptionPosition!.longitude - cameraPosition.target.longitude)
                    .abs() >
            0.0001);

    // Only update if zoom or position changed significantly
    if (!zoomChanged && !positionChanged) return;

    _lastCaptionZoom = zoom;
    _lastCaptionPosition = cameraPosition.target;

    // Cancel any existing timer
    _captionUpdateTimer?.cancel();

    // Debounce caption updates to avoid performance issues
    _captionUpdateTimer = Timer(const Duration(milliseconds: 300), () {
      if (!mounted || _mapController == null) return;

      if (zoom > _captionZoomThreshold) {
        // Show captions for visible pins
        _showCaptionsForVisiblePins(cameraPosition);
      } else {
        // Hide all captions
        _hideAllCaptions();
      }
    });
  }

  // Show captions for pins that are visible on screen when zoomed in
  void _showCaptionsForVisiblePins(CameraPosition cameraPosition) {
    // Get screen bounds
    final bounds = _getScreenBounds(cameraPosition);
    if (bounds == null) return;

    List<Map<String, dynamic>> allPinsData = [];

    // 🔧 CRITICAL FIX: Include optimistic pins from cache for caption display
    // First add regular pins (excluding test pins)
    if (_allPinsData.isNotEmpty) {
      allPinsData.addAll(_allPinsData.where((p) {
        final id = p['id'];
        return id is! String || !id.startsWith('test_');
      }));
    }

    // Then add optimistic pins from cache (these have captions too!)
    for (final pin in _pinCache.values) {
      final id = pin['id'];
      final isOptimistic = pin['isOptimistic'] == true;

      if (isOptimistic && (id is! String || !id.startsWith('test_'))) {
        allPinsData.add(pin);
        debugPrint('🎭 Including optimistic pin in caption check: $id');
      }
    }

    if (allPinsData.isEmpty) {
      debugPrint('🎭 No pins available for captions (including optimistic)');
      return;
    }

    debugPrint(
        '🎭 Caption check: ${allPinsData.length} pins available for captions (including optimistic)');

    // Find pins within screen bounds
    final visiblePins = allPinsData.where((pin) {
      final lat = pin['latitude'] as double?;
      final lng = pin['longitude'] as double?;

      if (lat == null || lng == null) {
        debugPrint('🔍 Pin ${pin['id']} has no coordinates, skipping');
        return false;
      }

      // Check if pin is within screen bounds
      final inBounds = lat >= bounds['southwest']!.latitude &&
          lat <= bounds['northeast']!.latitude &&
          lng >= bounds['southwest']!.longitude &&
          lng <= bounds['northeast']!.longitude;

      return inBounds;
    }).toList();

    // Show captions for visible pins with captions
    for (final pin in visiblePins) {
      final pinId = pin['id']?.toString() ?? pin['title']?.toString();
      final caption =
          pin['caption'] as String? ?? pin['description'] as String?;

      debugPrint('🎭 Pin ${pinId}: caption="${caption}"');

      if (pinId != null &&
          caption != null &&
          caption.isNotEmpty &&
          caption.trim() != '') {
        debugPrint('🎭 Showing caption for pin $pinId: "$caption"');
        _showCaptionPopup(pinId, caption);
      } else {
        debugPrint('🎭 Skipping pin $pinId - no valid caption');
      }
    }

    // Hide captions for pins no longer visible
    final visiblePinIds = visiblePins
        .map((pin) => pin['id']?.toString() ?? pin['title']?.toString())
        .where((id) => id != null)
        .cast<String>()
        .toSet();

    final pinsToHide = _visibleCaptionPopups.keys
        .where((pinId) => !visiblePinIds.contains(pinId))
        .toList();

    for (final pinId in pinsToHide) {
      _hideCaptionPopup(pinId);
    }
  }

  // Get approximate screen bounds based on camera position
  Map<String, LatLng>? _getScreenBounds(CameraPosition cameraPosition) {
    try {
      final center = cameraPosition.target;
      final zoom = cameraPosition.zoom;

      // Approximate bounds calculation (this is simplified)
      final latDelta = 180.0 / (1 << zoom.round()) * 2;
      final lngDelta = 360.0 / (1 << zoom.round()) * 2;

      return {
        'southwest':
            LatLng(center.latitude - latDelta, center.longitude - lngDelta),
        'northeast':
            LatLng(center.latitude + latDelta, center.longitude + lngDelta),
      };
    } catch (e) {
      debugPrint('Error calculating screen bounds: $e');
      return null;
    }
  }

  // Show animated caption popup for a specific pin
  void _showCaptionPopup(String pinId, String caption) {
    debugPrint(
        '🎭 _showCaptionPopup called for pin $pinId with caption: "$caption"');
    if (_visibleCaptionPopups[pinId] == true) return; // Already visible

    // Create animation controller if it doesn't exist
    if (!_captionAnimationControllers.containsKey(pinId)) {
      final controller = AnimationController(
        duration: const Duration(milliseconds: 600),
        vsync: this,
      );

      final animation = Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.elasticOut,
      ));

      _captionAnimationControllers[pinId] = controller;
      _captionAnimations[pinId] = animation;
    }

    _visibleCaptionPopups[pinId] = true;
    _captionAnimationControllers[pinId]?.forward();

    // Add a subtle haptic feedback
    HapticFeedback.lightImpact();

    if (mounted) {
      setState(() {});
    }

    // IMPORTANT: Also notify parent widget about the caption display
    // This ensures the parent map_screen.dart also shows the caption
    debugPrint('🎭 Notifying parent about caption display for pin: $pinId');
    if (widget.onShowCaptionToParent != null) {
      widget.onShowCaptionToParent!(pinId, caption);
      debugPrint('🎭 ✅ Parent callback called for caption display');
    } else {
      debugPrint('🎭 ❌ No parent callback available for caption display');
    }
  }

  // Hide animated caption popup for a specific pin
  void _hideCaptionPopup(String pinId) {
    if (_visibleCaptionPopups[pinId] != true) return; // Already hidden

    _captionAnimationControllers[pinId]?.reverse().then((_) {
      if (mounted) {
        setState(() {
          _visibleCaptionPopups[pinId] = false;
        });
      }
    });
  }

  // Hide all caption popups
  void _hideAllCaptions() {
    for (final pinId in _visibleCaptionPopups.keys.toList()) {
      _hideCaptionPopup(pinId);
    }
  }

  // Build caption popup widgets for visible pins
  List<Widget> _buildCaptionPopups() {
    final popups = <Widget>[];

    for (final pinId in _visibleCaptionPopups.keys) {
      if (_visibleCaptionPopups[pinId] == true) {
        final animation = _captionAnimations[pinId];
        if (animation != null) {
          Map<String, dynamic> pin = {};

          // First try to find in all pins data
          pin = _allPinsData.firstWhere(
            (p) => p['id']?.toString() == pinId,
            orElse: () => <String, dynamic>{},
          );

          // If not found, fall back to real pin data and test pin data
          if (pin.isEmpty && _realPinData.containsKey(pinId)) {
            pin = Map<String, dynamic>.from(_realPinData[pinId]!);
            pin['id'] = pinId;
          } else if (pin.isEmpty) {
            pin = _testPinsData.firstWhere(
              (p) => (p['id']?.toString() ?? p['title']?.toString()) == pinId,
              orElse: () => <String, dynamic>{},
            );
          }

          if (pin.isNotEmpty) {
            popups.add(_buildCaptionPopup(pin, animation));
          }
        }
      }
    }

    return popups;
  }

  // Build individual caption popup widget
  Widget _buildCaptionPopup(
      Map<String, dynamic> pin, Animation<double> animation) {
    return FutureBuilder<Offset?>(
      future: _getPinScreenPosition(pin),
      builder: (context, snapshot) {
        if (!snapshot.hasData || snapshot.data == null) {
          return const SizedBox.shrink();
        }

        final screenPosition = snapshot.data!;
        final caption =
            pin['caption'] as String? ?? pin['description'] as String? ?? '';

        if (caption.trim().isEmpty) {
          return const SizedBox.shrink();
        }

        return Positioned(
          left: screenPosition.dx - 75, // Center the popup
          top: screenPosition.dy -
              135, // Position even higher above the pin (was -120)
          child: IgnorePointer(
            child: AnimatedBuilder(
              animation: animation,
              builder: (context, child) {
                return Transform.scale(
                  scale: animation.value.clamp(
                      0.0, 2.0), // Allow some overshoot but clamp to safe range
                  child: Transform.translate(
                    offset: Offset(
                        0,
                        (1 - animation.value.clamp(0.0, 1.0)) *
                            -10), // Slide up animation
                    child: Opacity(
                      opacity: animation.value.clamp(0.0, 1.0),
                      child: Container(
                        constraints: const BoxConstraints(
                            maxWidth: 140), // Smaller width (was 160)
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 6), // Smaller padding
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Colors.black.withOpacity(
                                  0.7), // More transparent (was 0.9)
                              Colors.grey[900]!.withOpacity(
                                  0.7), // More transparent (was 0.9)
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: Colors.white.withOpacity(
                                0.15), // Slightly more transparent border (was 0.2)
                            width: 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(
                                  0.3), // More transparent shadow (was 0.4)
                              blurRadius: 12,
                              offset: const Offset(0, 6),
                            ),
                            BoxShadow(
                              color: Colors.cyan.withOpacity(
                                  (0.15 * animation.value).clamp(0.0,
                                      1.0)), // More transparent glow (was 0.2)
                              blurRadius: 20,
                              offset: const Offset(0, 0),
                            ),
                          ],
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Container(
                              width: 6, // Smaller dot (was 8)
                              height: 6, // Smaller dot (was 8)
                              decoration: BoxDecoration(
                                color: Colors.cyan,
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.cyan.withOpacity(0.6),
                                    blurRadius: 3, // Smaller blur (was 4)
                                    spreadRadius: 0.5, // Smaller spread (was 1)
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 6), // Smaller spacing (was 8)
                            Flexible(
                              child: Text(
                                caption,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 11, // Smaller text (was 13)
                                  fontWeight: FontWeight.w500, // Lighter weight
                                  shadows: [
                                    Shadow(
                                      color: Colors.black,
                                      blurRadius: 2,
                                      offset: Offset(0, 1),
                                    ),
                                  ],
                                ),
                                textAlign: TextAlign.center,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  // Get screen position for a pin
  Future<Offset?> _getPinScreenPosition(Map<String, dynamic> pin) async {
    if (_mapController == null || _currentPosition == null) return null;

    try {
      final lat = pin['latitude'] as double? ??
          (_currentPosition!.latitude + (pin['offsetLat'] as double? ?? 0.0));
      final lng = pin['longitude'] as double? ??
          (_currentPosition!.longitude + (pin['offsetLng'] as double? ?? 0.0));

      final screenLocation =
          await _mapController!.toScreenLocation(LatLng(lat, lng));
      return Offset(screenLocation.x.toDouble(), screenLocation.y.toDouble());
    } catch (e) {
      debugPrint('Error getting pin screen position: $e');
      return null;
    }
  }
}